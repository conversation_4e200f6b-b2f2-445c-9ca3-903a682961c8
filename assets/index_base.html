<!doctype html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <title>Spirulae | Web-Based Math Visualization</title>
    <meta name="description" content="GPU-accelerated math function graphers in web browsers, both 3D and 2D." />

    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <link rel="canonical" href="https://harry7557558.github.io/spirulae/" />

    <link rel="stylesheet" href="./assets/style.css" />

</head>

<body>

   <div id="s" style="position:absolute;top:0;height:0px;"></div>

    <div id="container">

      <h1 style="text-align:center;margin-bottom:0;font-family:Arial,Helvetica;">Spirulae:
         Web-Based Math Visualization</h1>
         <p style="text-align:center;">
            <em style="color:#555">by <PERSON> updated {%date%}</em>
         </p>
         <p style="text-align:center;margin-top:-0.5em;">
            <a href="https://github.com/harry7557558/spirulae/" target="_blank">GitHub</a> ⋅
            <a href="https://spirulae.github.io/gallery/">Gallery</a>
         </p>

         <div class="tool-gallery">
         <a href="https://harry7557558.github.io/spirulae/implicit3/" class="a">
            <img src="assets/img-implicit3.jpg" style="width:100%" />
            <br/>
            3D Implicit Surface Grapher
         </a>
         <a href="https://harry7557558.github.io/spirulae/paramsurf/" class="a">
            <img src="assets/img-paramsurf.jpg" style="width:100%" />
            <br/>
            3D Parametric Surface Grapher
         </a>
         <a href="https://harry7557558.github.io/spirulae/complex/" class="a">
            <img src="assets/img-complex.jpg" style="width:100%" />
            <br/>
            Complex Domain Coloring Grapher
         </a>
         <a href="https://harry7557558.github.io/spirulae/complex3/" class="a">
            <img src="assets/img-complex3.jpg" style="width:100%" />
            <br/>
            3D Complex Function Grapher
         </a>
         <a href="https://harry7557558.github.io/spirulae/implicit3-rt/" class="a">
            <img src="assets/img-implicit3rt.jpg" style="width:100%" />
            <br/>
            Implicit Surface Path Tracer<br />(powerful GPU required)
         </a>
         <a href="https://harry7557558.github.io/spirulae/meshgen3/" class="a">
            <img src="assets/img-meshgen3.jpg" style="width:100%" />
            <br/>
            3D Implicit Mesh Generator (dev)<br /> &nbsp;
         </a>
         <a href="https://harry7557558.github.io/spirulae/ode2/" class="a">
            <img src="assets/img-ode2.jpg" style="width:100%" />
            <br/>
            2D Vector Field Grapher (dev)
         </a>
         <a href="https://harry7557558.github.io/spirulae/meshgen2/" class="a">
            <img src="assets/img-meshgen2.jpg" style="width:100%" />
            <br/>
            2D to 3D Mesh Generator (dev)
         </a>
         </div>

         <div class="home-md-content">
         {%index.md%}
         </div>

         <p style="text-align: center;">
         <a href="#s">Back to top</a>
         </p>
    </div>

</body>

</html>