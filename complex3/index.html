<!doctype html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <title>3D Complex Domain Coloring Grapher | Spirulae</title>
    <meta name="description"
        content="A 3D complex function grapher that supports infinite domain, variable/function definition, and special functions." />
    <meta name="keywords" content="complex, 3d, function, domain coloring, webgl" />
    <meta property="og:image" content="../assets/gallery-complex3-tan.jpg" />
    <link rel="image_src" href="../assets/gallery-complex3-tan.jpg" />

    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <link rel="canonical" href="https://harry7557558.github.io/spirulae/complex3/" />

    <link rel="stylesheet" href="../styles/style.css" />

</head>

<body>
    <canvas id="canvas"></canvas>

    <div id="legend">
        <div id="fps"></div>
        <svg id="axes" width="80" height="80">
            <g transform="translate(40,40)">
                <circle cx="0" cy="0" r="40" stroke="none" fill="rgba(48,48,48,0.6)"></circle>
                <line id="axis-x" x1="0" y1="0" x2="0" y2="0" stroke="rgb(250,50,80)" stroke-width="2.5"></line>
                <line id="axis-y" x1="0" y1="0" x2="0" y2="0" stroke="rgb(140,220,0)" stroke-width="2.5"></line>
                <line id="axis-z" x1="0" y1="0" x2="0" y2="0" stroke="rgb(40,140,250)" stroke-width="2.5"></line>
            </g>
            <text id="legend-text" x="40" y="68" text-anchor="middle" alignment-baseline="top" stroke="none"
                fill="rgba(255,255,255,0.7)" font-family="monospace" font-size="1.0em"></text>
        </svg>
    </div>

    <div id="control">
        <select id="builtin-functions"></select>
        <span title="Update equation (Alt+Enter)"><button id="button-update">update</button></span>
        <a href='#' style="float:right"
            onclick='javascript:event.preventDefault();document.getElementById("help-menu").style.visibility="visible";'>help</a>
        <br />
        <span title="Display a preview of the input equation"><input type="checkbox" id="checkbox-latex"
                checked />equation preview</span>&ensp;
        <span title="Automatically update the shader on input"><input type="checkbox" id="checkbox-auto-compile"
                checked />auto-update</span>&ensp;
        <br />
        <textarea id="equation-input" spellcheck="false" autocapitalize="off" autocorrect="off"
            data-gramm="false"></textarea>
        <br />
        <span><select id="select-hz">
                <option value="z.x">Re(z)</option>
                <option value="z.y">Im(z)</option>
                <option value="length(z)" selected>|z|</option>
                <option value="log(length(z))">ln(|z|)</option>
                <option value="log(length(z))+length(z)">ln(|z|)+|z|</option>
                <option value="log(length(z)+1.)">ln(|z|+1)</option>
                <option value="1./length(z)">1/|z|</option>
                <option value="-log(length(z))">-ln(|z|)</option>
                <option value="sign(z.x)*asinh(abs(z.x))">asinh(Re(z))</option>
                <option value="sign(z.y)*asinh(abs(z.y))">asinh(Im(z))</option>
            </select>graph</span>&ensp;
        <span><select id="select-step">
                <option value="0.04">low</option>
                <option value="0.01">medium</option>
                <option value="0.004" selected>high</option>
                <option value="0.001">ex-high</option>
            </select>quality</span>
        <br />
        <span title="Clip the shape inside a box"><select id="select-clip">
            <option value="0">no</option>
            <option value="1" selected>box</option>
            <option value="2">sphere</option>
        </select>clip</span>
        (<span title="Fixed box size independent of zooming"><input type="checkbox"
                id="checkbox-clip-fixed" />fixed</span>)
        <br />
        <span title="Show grid on the surface"><input type="checkbox" id="checkbox-grid" checked />grid</span>&ensp;
        <span title="Red-highlight discontinuities with sign change for opaque surfaces"><input type="checkbox"
                id="checkbox-discontinuity" checked />highlight discontinuity</span>&ensp;
        <br />
        <span><i>θ<sub>light</sub></i>&nbsp;<input type="range" id="slider-theta" min="-90" max="270" value="30"
                style="width:60px" /></span>
        <span><i>φ<sub>light</sub></i>&nbsp;<input type="range" id="slider-phi" min="0" max="180" value="30"
                style="width:60px" /></span>
        <span title="Light theme"><input type="checkbox" id="checkbox-light" checked />☼</span>
        <br />
        <span title="Scaling of the z-axis"><input type="range" id="slider-zscale"
                style="width:70px" />z-scale</span>&ensp;
        <span title="Domain coloring brightness"><input type="range" id="slider-brightness"
                style="width:70px" />brightness</span>
        <br />
        <p id="error-message" style="display:none"></p>
    </div>

    <div id="mathjax-preview" style="left:0px;top:0px;display:none"></div>

    <div id="help-menu" style="visibility:hidden">
        <div id="help-menu-hide" onclick='document.getElementById("help-menu").style.visibility="hidden"'>×</div>
    </div>

    <script>
        (function () {
            // refresh cache every one hour
            function loadScript(src) {
                var hour = Math.floor(Date.now() / 3600000);
                var script = document.createElement("script");
                script.src = src + "?nocache=" + hour;
                document.head.appendChild(script);
            }
            loadScript("../scripts/parameter.js");
            loadScript("../scripts/functions.js");
            loadScript("../scripts/parser.js");
            loadScript("../scripts/codegen.js");
            loadScript("../scripts/render-gl.js");
            loadScript("../scripts/render-3d.js");
            loadScript("../scripts/render-raymarch.js");
            loadScript("../scripts/latex.js");
            loadScript("script.js");
        })();
    </script>

</body>

</html>