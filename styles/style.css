#canvas, #emscripten-canvas {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: black;
    touch-action: none;
}

.cursor-crosshair {
    cursor: crosshair;
}

#legend {
    position: fixed;
    left: 0;
    top: 0;
    padding: 10px;
    user-select: none;
}

#fps {
    margin: 0 0 2px -6px;
    padding: 0 6px;
    width: fit-content;
    font-family: monospace;
    font-size: 20px;
    color: white;
    user-select: none;
    background-color: rgba(0, 0, 0, 0.3);
}

#axes {
    display: block;
    margin-top: 5px;
    margin-left: -2px;
}

svg:hover .axis-3d-active {
    fill: rgba(196, 128, 48, 0.6);
}

#axis-x, #axis-y, #axis-z, #legend-text {
    pointer-events: none;
}

#control {
    position: fixed;
    top: 10px;
    right: 10px;
    box-sizing: border-box;
    width: min-content;
    height: min-content;
    min-width: 240px;
    max-width: 80%;
    max-height: 95%;
    overflow-x: hidden;
    overflow-y: auto;
    background-color: hsl(240, 20%, 20%, 0.8);
    padding: 15px 20px;
    color: white;
    font-size: 0.95em;
    font-family: Arial, Helvetica, sans-serif;
    white-space: nowrap;
    z-index: 998;
}

#error-message {
    display: inline-block;
    margin: 0.5em -0.1em;
    padding: 0.2em 0.3em;
    white-space: normal;
    margin-bottom: 0
}

#value-display {
    font-family: monospace;
}

#equation-input {
    width: 100%;
    height: 8em;
    min-width: 240px;
    min-height: 80px;
    resize: horizontal;
    font-family: 'Consolas', 'Courier New', monospace;
    font-size: 1.0em;
    margin: 0.2em 0 0;
    word-break: break-all;
}

.foldable[folded="true"] {
    margin-top: -0.1em;
    height: 0.9em;
    overflow: hidden;
}

.foldable-name {
    cursor: pointer;
}

.foldable[folded="false"] .foldable-name {
    float: right;
    width: 0.6em;
    overflow: hidden;
}

.foldable[folded="true"] .foldable-name {
    display: block;
    margin-top: -0.2em;
    margin-bottom: 0.5em;
    text-decoration: underline;
}

input, select, button {
    margin: 0.1em 0.2em 0.1em 0;
    display: inline-block;
    vertical-align: middle;
}

input[type="range"] {
    -webkit-appearance: none;
    appearance: none;
    height: 1.0em;
    background: hsl(120, 0%, 80%, 0.5);
    outline: none;
}

input[type="range"]:hover {
    background: hsl(120, 0%, 80%, 0.7);
}

input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 0.0em;
    height: 1.1em;
    cursor: pointer;
    border: 0.1em solid hsl(120, 80%, 100%);
    border-radius: 0.4em;
}

input[type="range"]::-moz-range-thumb {
    width: 0.0em;
    height: 1.0em;
    cursor: pointer;
    border: 0.1em solid hsl(120, 80%, 100%);
    border-radius: 0.4em;
}

input[type="checkbox"] {
    -webkit-appearance: none;
    appearance: none;
    background-color: rgba(232, 232, 232, 0.5);
    width: 1em;
    height: 1em;
    border-radius: 0.2em;
    display: inline-grid;
    place-content: center;
}

input[type="checkbox"]:hover {
    background-color: rgba(232, 232, 232, 1);
}

input[type="checkbox"]::before {
    content: "";
    width: 0.7em;
    height: 0.7em;
    transform: scale(0);
    background-color: white;
    clip-path: polygon(14% 44%, 0 65%, 50% 100%, 100% 16%, 80% 0%, 43% 62%);
}

input[type="checkbox"]:checked::before {
    transform: scale(1);
}

input[type="checkbox"]:checked {
    /* background-color: hsl(30,100%,70%); */
    background-color: rgb(0, 120, 237);
}

input[type="checkbox"]:checked:hover {
    background-color: dodgerblue;
}

.checkbox-light[type="checkbox"]:hover {
    background-color: yellow;
}

.checkbox-light[type="checkbox"]:checked {
    background-color: darkorange;
}

.checkbox-light[type="checkbox"]:checked:hover {
    background-color: orange;
}


hr {
    color: rgba(255, 255, 255, 0.4);
    margin: 0.5em 0;
}

#help-menu {
    display: block;
    position: fixed;
    margin: 20px auto;
    left: 50%;
    transform: translateX(-50%);
    width: 80%;
    height: 80%;
    max-width: 800px;
    max-height: 800px;
    background-color: hsl(300, 30%, 20%, 0.9);
    padding: 20px 32px 40px 40px;
    font-family: Arial, Helvetica, sans-serif;
    font-size: 0.95em;
    line-height: 1.2em;
    color: white;
    overflow-y: scroll;
    z-index: 999;
}

#help-menu-hide {
    display: block;
    position: absolute;
    right: 40px;
    top: 20px;
    font-size: 2em;
    text-decoration: none;
    cursor: pointer;
}

#help-menu-hide:hover {
    text-decoration: underline;
}

#help-menu-links {
    float: right;
    padding-right: 60px;
    padding-top: 1.8em;
    text-align: right;
}

#help-menu h2 {
    line-height: 1.2em;
    font-size: 1.5em;
    margin: 1.0em 0;
}

#help-menu h3 {
    font-size: 1.3em;
    margin: 1.0em 0 0.6em;
}

#help-menu p {
    font-size: 1em;
    margin: 0.6em 0;
}

#help-menu code {
    color: #fea;
    font-family: monospace;
}

#help-menu img {
    max-width: 100%;
    max-height: 350px;
    object-fit: contain;
    margin: 0.3em 0;
}

a {
    text-decoration: underline;
    color: white;
}

#mathjax-preview {
    position: fixed;
    margin: 0;
    padding: 0;
    color: white;
    cursor: move;
    font-size: 1.1em;
    pointer-events: none;
    touch-action: none;
}

#mathjax-preview div {
    display: block;
    margin: 0;
    width: min-content;
    padding: 0em;
    pointer-events: all;
}

#mathjax-preview span {
    display: inline-block;
    margin: 0.3em;
    padding: 0em 0.3em;
    background-color: rgba(0, 0, 0, 0.55);
    box-shadow: 0 0 0.35em 0.35em rgba(0, 0, 0, 0.55);
}