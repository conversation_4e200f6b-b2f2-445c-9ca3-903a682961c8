<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8" />
    <title>Code Generator | Spirulae</title>
    <link rel="canonical" href="https://harry7557558.github.io/spirulae/autodiff/" />

    <style>
        body {
            width: 100%;
            max-width: 800px;
        }

        textarea {
            width: 100%;
        }

        #code-fun {
            min-height: 100px;
        }

        #expr {
            min-height: 150px;
        }

        pre {
            white-space: pre-wrap;
        }
    </style>

</head>

<body>
    <h2>Code Generation and Automatic Differentiation Tool</h2>

    <p>Load preset:
        <select id="presets"></select>
    </p>

    <hr/>

    <p>Language:
        <select id="lang">
            <option value="cppf">C/C++ (float)</option>
            <option value="cppd">C/C++ (double)</option>
            <option value="glsl">GLSL</option>
            <option value="js">JavaScript</option>
        </select>
    </p>
    <p>Code function</p>
    <textarea id="code-fun">
double fun(
    double x, double y, double z,
    double *dvdx, double *dvdy, double *dvdz
) {
{%funbody%};
    *dvdx = {%v;x%}, *dvdy = {%v;y%}, *dvdz = {%v;z%};
    return {%v%};
}</textarea>
    <p>Code line</p>
    <textarea id="code-def">    double {%varname%} = {%expr%};</textarea>
    <p>Code joiner (may contain line breaks)</p>
    <textarea id="code-joiner">

</textarea>

    <hr />

    <p>Independent variable(s)</p>
    <textarea id="vars-i">x, y, z</textarea>

    <p>Dependent variable(s)</p>
    <textarea id="vars-d">v</textarea>

    <p>Expression</p>
    <textarea id="expr">v=|z-x^2*sin(y)|</textarea>

    <hr />

    <p>Generated code</p>
    <pre id="code">

    </pre>

    <br/><hr/>

    <p>This tool is a part of <a href="https://harry7557558.github.io/spirulae/" target="_blank">Spirulae</a>.</p>

    <script>
        (function () {
            // refresh cache every one hour
            function loadScript(src) {
                var hour = Math.floor(Date.now() / 3600000);
                var script = document.createElement("script");
                script.src = src + "?nocache=" + hour;
                document.head.appendChild(script);
            }
            loadScript("../scripts/functions.js");
            loadScript("../scripts/parser.js");
            loadScript("../scripts/codegen.js");
            loadScript("script.js");
        })();
    </script>

</body>

</html>