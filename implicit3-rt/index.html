<!doctype html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <title>Path Tracer | Spirulae</title>
    <meta name="description"
        content="A path tracing version of my 3D implicit surface grapher." />
    <meta name="keywords"
        content="implicit surface, function, graphing calculator, ray tracing, path tracing, webgl" />

    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <link rel="canonical" href="https://harry7557558.github.io/spirulae/implicit3-rt/" />

    <link rel="stylesheet" href="../styles/style.css" />

</head>

<body>
    <canvas id="canvas"></canvas>

    <div id="legend">
        <div id="fps"></div>
        <svg id="axes" width="80" height="80">
            <g transform="translate(40,40)">
                <circle cx="0" cy="0" r="40" stroke="none" fill="rgba(48,48,48,0.6)"></circle>
                <line id="axis-x" x1="0" y1="0" x2="0" y2="0" stroke="rgb(250,50,80)" stroke-width="2.5"></line>
                <line id="axis-y" x1="0" y1="0" x2="0" y2="0" stroke="rgb(140,220,0)" stroke-width="2.5"></line>
                <line id="axis-z" x1="0" y1="0" x2="0" y2="0" stroke="rgb(40,140,250)" stroke-width="2.5"></line>
            </g>
            <text id="legend-text" x="40" y="68" text-anchor="middle" alignment-baseline="top" stroke="none"
                fill="rgba(255,255,255,0.7)" font-family="monospace" font-size="1.0em"></text>
        </svg>
    </div>

    <div id="control">
        <select id="builtin-states"></select>
        <span title="Update equation (Alt+Enter)"><button id="button-update">update</button></span>
        <a href='#' style="float:right"
            onclick='javascript:event.preventDefault();document.getElementById("help-menu").style.visibility="visible";'>help</a>
        <br />
        <span title="Display a preview of the input equation"><input type="checkbox" id="checkbox-latex"
                checked />equation preview</span>&nbsp;<span title="Light equation preview theme"><input
                    type="checkbox" id="checkbox-light" class="checkbox-light" checked /></span>&ensp;
        <span title="Automatically update the shader on input"><input type="checkbox" id="checkbox-auto-compile"
                checked />auto-update</span>&ensp;
        <br />
        <textarea id="equation-input" spellcheck="false" autocapitalize="off" autocorrect="off"
            data-gramm="false"></textarea>
        <br />
        <hr />
    <div class="foldable" id='folder-basic' folded="false">
        <span class="foldable-name"></span>
        <span><select id="select-step">
                <option value="0.04">low</option>
                <option value="0.01" selected>medium</option>
                <option value="0.004">high</option>
                <option value="0.001">ultra high</option>
            </select>precision</span>&ensp;
        <span title="Maximum samples per pixel"><select id="select-spp">
                <option value="1">2⁰</option>
                <option value="4">2²</option>
                <option value="16">2⁴</option>
                <option value="64">2⁶</option>
                <option value="256" selected>2⁸</option>
                <option value="1024">2¹⁰</option>
                <option value="4096">2¹²</option>
                <option value="16384">2¹⁴</option>
            </select>spp</span>
        <br/>
        <span title="Maximum light path depth"><select id="select-light-path-depth">
                <option value="8">8</option>
                <option value="16">16</option>
                <option value="32">32</option>
                <option value="64" selected>64</option>
                <option value="128">128</option>
                <option value="256">256</option>
                <option value="512">512</option>
                <option value="1024">1024</option>
            </select>path depth</span>&ensp;
        <span title="Number of samples per frame per pixel, otherwise with an FPS controller"><select id="select-samples">
                <option value="0.0625">2⁻⁴ samples</option>
                <option value="0.25">2⁻² samples</option>
                <option value="1">2⁰ samples</option>
                <option value="4">2² samples</option>
                <option value="16">2⁴ samples</option>
                <option value="10">10 fps</option>
                <option value="20" selected>20 fps</option>
                <option value="30">30 fps</option>
                <option value="40">40 fps</option>
                <option value="50">50 fps</option>
            </select></span>
        <span style="display:none" title="Enable experimental Multiple Importance Sampling (MIS) for high-variance scenes"><input type="checkbox"
                id="checkbox-mis" />MIS (beta)</span>&ensp;
        <br />
        <span title="Shape clipping boundary"><select id="select-clip">
            <option value="1" selected>box</option>
            <option value="2">sphere</option>
        </select>clip</span>&ensp;
            <span style="display:none;" title="Visualize scalar field with volume contour lines"><select id="select-field">
                <option value="0" selected>no</option>
                <option value="1">linear</option>
                <option value="2">log</option>
            </select>field</span>
        <span title="Show surface at clipping boundary"><input
            type="checkbox" id="checkbox-closed" checked/>closed</span>&ensp;
        <span title="Use y-up coordinate system convension"><input type="checkbox"
                id="checkbox-yup" /><i>y</i>-up</span>&ensp;
        <br />
        <span><select id="select-denoise" style="width:5.8em;">
            <option value="null" selected>no</option>
            <!-- <option value="temp">Test</option> -->
            <option value="runet2gan2">ResUNet 2 (GAN) 2</option>
            <option value="runet2gan">ResUNet 2 (GAN)</option>
            <option value="runet2">ResUNet 2</option>
            <!-- <option value="runet1an">ResUNet [an]</option> -->
            <option value="runet1">ResUNet</option>
            <option value="unet1">UNet</option>
            <option value="resnet1">ResNet</option>
        </select>denoise</span>
        <span><select id="select-tonemap" style="width:4.2em;">
            <option value="None" selected>no</option>
            <option value="Aces">ACES</option>
            <option value="Filmic">Filmic</option>
            <option value="Reinhard">Reinhard</option>
            <option value="Uncharted2">Uncharted 2</option>
        </select>tonemap</span>&ensp;
        <br />
        <span>scale</span>
        <span title="object size"><input type="range" id="slider-scale1" style="width:100px" /></span>
        <span title="plane grid size"><input type="range" id="slider-scale2" style="width:100px" /></span>
        <br />
    </div>
    <hr />
    <div class="foldable" id='folder-object' folded="true">
        <span class="foldable-name"></span>
        <span title="Surface color mode"><select id="select-color">
                <option value="0">default</option>
                <option value="1">normal</option>
                <option value="2" selected>gradient</option>
            </select>color</span>&ensp;
        <br />
        <span title="Show grid on the surface"><input type="checkbox" id="checkbox-grid" />grid</span>&ensp;
        <span title="Red-highlight discontinuities with sign change for opaque surfaces"><input type="checkbox"
                id="checkbox-discontinuity" checked />highlight discontinuity</span>
        <br />
        <span title="object opacity"><span>opacity</span>&nbsp;<input type="range" id="slider-opacity" style="width:80px" /></span>
        <span title="object index of refraction"><span>ior</span>&nbsp;<input type="range" id="slider-ior" style="width:80px" /></span>
        <br />
        <span>roughness</span>
        <span title="object roughness"><input type="range" id="slider-roughness1" style="width:70px" /></span>
        <span>emit</span>
        <span title="object surface emission intensity"><input type="range" id="slider-emission1" style="width:60px" /></span>
        <br />
        <span>metallic</span>
        <span title="object metalness"><input type="range" id="slider-metallic1" style="width:65px" /></span>
        <!-- <span>diffuse</span>
        <span title="object diffuseness"><input type="range" id="slider-diffuse1" style="width:65px" /></span> -->
        <span>tint</span>
        <span title="object opaque reflection tint"><input type="range" id="slider-tint1" style="width:65px" /></span>
        <br />
        <span>absorb</span>
        <span title="object volume absorption intensity"><input type="range" id="slider-absorb1" style="width:60px" /></span>
        <span>emit[v]</span>
        <span title="object volume emission intensity"><input type="range" id="slider-vemission1" style="width:60px" /></span>
        <br />
        <span>scatter</span>
        <span title="object volume scattering intensity"><input type="range" id="slider-scatter1" style="width:80px" /></span>
        <span>aniso</span>
        <span title="object volume scattering anisotropy"><input type="range" id="slider-scatter-aniso1" style="width:60px" /></span>
        <br />
    </div>
    <hr />
    <div class="foldable" id='folder-background' folded="true">
        <span class="foldable-name"></span>
        <span>brightness</span>
        <span title="plane overall color brightness"><input type="range" id="slider-brightness2" style="width:55px" /></span>
        <span>contrast</span>
        <span title="plane color contrast"><input type="range" id="slider-contrast2" style="width:50px" /></span>
        <br />
        <span>roughness</span>
        <span title="plane roughness"><input type="range" id="slider-roughness2" style="width:70px" /></span>
        <span>emit</span>
        <span title="plane emission intensity"><input type="range" id="slider-emission2" style="width:60px" /></span>
        <br />
        <span>metallic</span>
        <span title="plane metalness"><input type="range" id="slider-metallic2" style="width:60px" /></span>
        <span>ior</span>
        <span title="plane index of refraction"><input type="range" id="slider-ior2" style="width:40px" /></span>
        <span>tint</span>
        <span title="plane reflection tint"><input type="range" id="slider-tint2" style="width:40px" /></span>
        <br />
        <span>absorb</span>
        <span title="atmospheric absorption intensity"><input type="range" id="slider-absorb2" style="width:80px" /></span>
        <span title="near-ground absorption layer thickness">depth&nbsp;<input type="range" id="slider-vdecay-abs" style="width:60px" /></span>
        <br />
        &emsp;
        <span title="atmospheric absorption hue">hue&nbsp;<input type="range" id="slider-vabsorb-hue" style="width:90px" /></span>
        <span title="atmospheric absorption chroma">chroma&nbsp;<input type="range" id="slider-vabsorb-chr" style="width:50px" /></span>
        <br />
        &emsp;
        <span title="atmospheric emission intensity">emit[v]&nbsp;<input type="range" id="slider-vemit" style="width:80px" /></span>
        <!-- <span title="atmospheric emission color tint">tint&nbsp;<input type="range" id="slider-vemit-tint" style="width:60px" /></span> -->
        <br />
        <span>scatter</span>
        <span title="atmospheric scattering intensity"><input type="range" id="slider-scatter2" style="width:80px" /></span>
        <span>aniso</span>
        <span title="atmospheric scattering anisotropy"><input type="range" id="slider-scatter-aniso2" style="width:60px" /></span>
        <br />
        &emsp;
        <span title="near-ground scattering layer thickness">depth&nbsp;<input type="range" id="slider-vdecay-sca" style="width:70px" /></span>
        <span title="near-ground scattering layer boundary sharpness">sharp&nbsp;<input type="range" id="slider-vsharp-sca" style="width:70px" /></span>
        <br />
    </div>
    <hr />
    <div class="foldable" id='folder-lighting' folded="true">
        <span class="foldable-name"></span>
        <span>light&nbsp;<i>θ</i>&nbsp;<input type="range" id="slider-theta" style="width:100px" /></span>
        <span><i>φ</i>&nbsp;<input type="range" id="slider-phi" style="width:70px" /></span>
        <br />
        <span title="total background light intensity">intensity&nbsp;<input type="range" id="slider-light-intensity" style="width:70px" /></span>
        <span title="factor of sky light">sky&nbsp;<input type="range" id="slider-light-sky" style="width:70px" /></span>
        <br />
        <span title="ambient background light factor">ambient&nbsp;<input type="range" id="slider-light-ambient" style="width:35px" /></span>
        <span title="transition from point to dome light">soft&nbsp;<input type="range" id="slider-light-softness" style="width:40px" /></span>
        <span title="sun light boundary sharpness">hard&nbsp;<input type="range" id="slider-light-hardness" style="width:35px" /></span>
        <br />
    </div>
    <hr />
    <div class="foldable" id='folder-camera' folded="true">
        <span class="foldable-name"></span>
        <span title="camera field of view">fov&nbsp;<input type="range" id="slider-camera-fov" style="width:75px" /></span>
        <span title="camera exposure">exposure&nbsp;<input type="range" id="slider-camera-exposure" style="width:70px" /></span>
        <br />
        <span title="camera roll angle">roll&nbsp;<input type="range" id="slider-camera-roll" style="width:75px" /></span>
        <span title="camera lens distortion">distortion&nbsp;<input type="range" id="slider-camera-distortion" style="width:75px" /></span>
        <br />
        <span title="camera focal length">focal&nbsp;<input type="range" id="slider-focal-length" style="width:70px" /></span>
        <span title="camera aperture size">aperture&nbsp;<input type="range" id="slider-aperture-size" style="width:75px" /></span>
        <br />
        <span title="number of aperture blades">shape&nbsp;<input type="range" id="slider-aperture-shape" style="width:75px" /></span>
        <span title="camera aperture rotation">rotate&nbsp;<input type="range" id="slider-aperture-rotate" style="width:75px" /></span>
        <br />
    </div>
        <p id="error-message" style="display:none"></p>
    </div>

    <div id="mathjax-preview" style="left:0px;top:0px;display:none"></div>

    <div id="help-menu" style="visibility:hidden">
        <div id="help-menu-hide" onclick='document.getElementById("help-menu").style.visibility="hidden"'>×</div>
    </div>

    <script>
        (function () {
            // refresh cache every one hour
            function loadScript(src) {
                var hour = Math.floor(Date.now() / 3600000);
                var script = document.createElement("script");
                script.src = src + "?nocache=" + hour;
                document.head.appendChild(script);
            }
            loadScript("../scripts/parameter.js");
            loadScript("../scripts/functions.js");
            loadScript("../scripts/parser.js");
            loadScript("../scripts/codegen.js");
            loadScript("../scripts/render-gl.js");
            loadScript("../scripts/render-3d.js");
            loadScript("../scripts/render-rt.js");
            loadScript("../scripts/dnn.js");
            loadScript("../scripts/latex.js");
            loadScript("script.js");
            loadScript("denoise.js");
        })();
    </script>

</body>

</html>