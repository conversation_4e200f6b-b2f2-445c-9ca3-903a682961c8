{"dtype": "int8", "state_dict": {"econv0a.weight": {"shape": [16, 3, 3, 3], "m": 0.002124163335309111, "b": -0.026971443084008673, "offset": 0}, "econv0a.bias": {"shape": [16], "m": 0.0012358115456729331, "b": 0.0036552839647844004, "offset": 432}, "econv0b.weight": {"shape": [16, 16, 3, 3], "m": 0.002221136209167528, "b": 0.05361667221625521, "offset": 448}, "econv0b.bias": {"shape": [16], "m": 0.00047044220323465307, "b": -0.004173380569839964, "offset": 2752}, "econv1.weight": {"shape": [24, 16, 3, 3], "m": 0.001218134604593274, "b": -0.017645376700808513, "offset": 2768}, "econv1.bias": {"shape": [24], "m": 0.0006846857828846905, "b": -0.009963046870482398, "offset": 6224}, "econv2.weight": {"shape": [48, 24, 3, 3], "m": 0.0013263089643729912, "b": 0.013082542180360035, "offset": 6248}, "econv2.bias": {"shape": [48], "m": 0.0005530860725339952, "b": -0.005902603432372366, "offset": 16616}, "mconv1.weight": {"shape": [64, 48, 3, 3], "m": 0.0018181065843094087, "b": -0.006609983239293654, "offset": 16664}, "mconv1.bias": {"shape": [64], "m": 0.0005993299665780419, "b": -0.0004139292745425488, "offset": 44312}, "mconv2.weight": {"shape": [64, 64, 3, 3], "m": 0.003552265359992322, "b": -0.07973533501524005, "offset": 44376}, "mconv2.bias": {"shape": [64], "m": 0.00043673777084335614, "b": -0.004086271109676437, "offset": 81240}, "dconv2a.weight": {"shape": [64, 48, 4, 4], "m": 0.005187100293684978, "b": 0.0780504078281169, "offset": 81304}, "dconv2a.bias": {"shape": [48], "m": 0.0003560926338286377, "b": -0.00471206775207067, "offset": 130456}, "dconv2b.weight": {"shape": [48, 96, 3, 3], "m": 0.0031765991327713946, "b": -0.06937952370059736, "offset": 130504}, "dconv2b.bias": {"shape": [48], "m": 0.0004216464560952628, "b": 0.005457853635237958, "offset": 171976}, "dconv1a.weight": {"shape": [48, 24, 4, 4], "m": 0.00398202163271103, "b": -0.09810646661971023, "offset": 172024}, "dconv1a.bias": {"shape": [24], "m": 0.0003939230152913128, "b": 0.0008092986498747373, "offset": 190456}, "dconv1b.weight": {"shape": [24, 48, 3, 3], "m": 0.002252587484227993, "b": -0.03378983894640836, "offset": 190480}, "dconv1b.bias": {"shape": [24], "m": 0.0003637810306421816, "b": 0.007001090286010106, "offset": 200848}, "dconv0a.weight": {"shape": [24, 16, 4, 4], "m": 0.0031849054189828723, "b": -0.013025169189159691, "offset": 200872}, "dconv0a.bias": {"shape": [16], "m": 0.00034308525933949874, "b": 0.008057129100035275, "offset": 207016}, "dconv0b.weight": {"shape": [16, 32, 3, 3], "m": 0.0017915023973261355, "b": -0.04834071032153195, "offset": 207032}, "dconv0b.bias": {"shape": [16], "m": 0.00045325098269775486, "b": 0.013284531671969073, "offset": 211640}, "dconv0.weight": {"shape": [3, 16, 3, 3], "m": 0.003671043521755344, "b": -0.010419640239778483, "offset": 211656}, "dconv0.bias": {"shape": [3], "m": 0.0003135464851579262, "b": -0.009375585254802248, "offset": 212088}}}