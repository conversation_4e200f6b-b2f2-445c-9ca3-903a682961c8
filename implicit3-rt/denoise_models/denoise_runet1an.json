{"dtype": "int8", "state_dict": {"econv0a.weight": {"shape": [12, 11, 3, 3], "m": 0.002818577330752482, "b": 0.060369458219132976, "offset": 0}, "econv0a.bias": {"shape": [12], "m": 0.0011922210876009714, "b": 0.047524017116619244, "offset": 1188}, "econv0b.weight": {"shape": [12, 12, 3, 3], "m": 0.0014961093336671263, "b": -0.011558441834135375, "offset": 1200}, "econv0b.bias": {"shape": [12], "m": 0.0006029879443312365, "b": -0.0033079472485947198, "offset": 2496}, "econv1.weight": {"shape": [16, 12, 3, 3], "m": 0.0016894268699493287, "b": 0.01308917640189361, "offset": 2508}, "econv1.bias": {"shape": [16], "m": 0.0006491518937624417, "b": -0.006162644578860357, "offset": 4236}, "econv2.weight": {"shape": [24, 16, 3, 3], "m": 0.0012323311771291017, "b": 0.008067819069094345, "offset": 4252}, "econv2.bias": {"shape": [24], "m": 0.0006397583140308853, "b": -0.005187865691712729, "offset": 7708}, "mconv1.weight": {"shape": [32, 24, 3, 3], "m": 0.001408067331583571, "b": 0.01756331174863937, "offset": 7732}, "mconv1.bias": {"shape": [32], "m": 0.0006233525917144548, "b": 0.004633221658329759, "offset": 14644}, "mconv2.weight": {"shape": [32, 32, 3, 3], "m": 0.003059265471328184, "b": -0.15355533487755613, "offset": 14676}, "mconv2.bias": {"shape": [32], "m": 0.0005481261145937574, "b": 0.004396215981834539, "offset": 23892}, "dconv2a.weight": {"shape": [32, 24, 4, 4], "m": 0.0030905298385739887, "b": -0.04124532679747933, "offset": 23924}, "dconv2a.bias": {"shape": [24], "m": 0.00026654513491379035, "b": 0.005665889316862759, "offset": 36212}, "dconv2b.weight": {"shape": [24, 48, 3, 3], "m": 0.003493077807374053, "b": -0.09101891157391306, "offset": 36236}, "dconv2b.bias": {"shape": [24], "m": 0.0004951178068455946, "b": 0.03384351762804075, "offset": 46604}, "dconv1a.weight": {"shape": [24, 16, 4, 4], "m": 0.004496810668304539, "b": 0.09184780609289556, "offset": 46628}, "dconv1a.bias": {"shape": [16], "m": 0.0004368823378389259, "b": 0.010349759291067975, "offset": 52772}, "dconv1b.weight": {"shape": [16, 32, 3, 3], "m": 0.0025173296546636605, "b": 0.036760353546127555, "offset": 52788}, "dconv1b.bias": {"shape": [16], "m": 0.0004048960713239816, "b": 0.014935583735887818, "offset": 57396}, "dconv0a.weight": {"shape": [16, 12, 4, 4], "m": 0.0033326903168036013, "b": -0.061221859893020314, "offset": 57412}, "dconv0a.bias": {"shape": [12], "m": 0.0004919364557161435, "b": 0.004387611871237276, "offset": 60484}, "dconv0b.weight": {"shape": [12, 24, 3, 3], "m": 0.0026180203331508667, "b": -0.042729404788563685, "offset": 60496}, "dconv0b.bias": {"shape": [12], "m": 0.00037866420868427447, "b": -0.003535783105475754, "offset": 63088}, "dconv0.weight": {"shape": [3, 12, 3, 3], "m": 0.0012379966594360686, "b": 0.0034552257467102, "offset": 63100}, "dconv0.bias": {"shape": [3], "m": 4.996792332881848e-05, "b": -0.006421424611873656, "offset": 63424}}}