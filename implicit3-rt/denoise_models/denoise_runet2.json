{"dtype": "int8", "state_dict": {"convi.weight": {"shape": [12, 3, 3, 3], "m": 0.0031051250230199313, "b": 0.031211863554066743, "offset": 0}, "convi.bias": {"shape": [12], "m": 0.0007564227098198475, "b": 0.026123521244226483, "offset": 324}, "econv0a.weight": {"shape": [12, 12, 3, 3], "m": 0.0033833528612996194, "b": 0.12206245189184672, "offset": 336}, "econv0a.bias": {"shape": [12], "m": 0.0010048589863619962, "b": -0.038789265267141576, "offset": 1632}, "econv0b.weight": {"shape": [12, 12, 3, 3], "m": 0.003145901805003359, "b": 0.006229444566289488, "offset": 1644}, "econv0b.bias": {"shape": [12], "m": 0.0006996639061198691, "b": -0.018050814793098655, "offset": 2940}, "econv1a.weight": {"shape": [16, 12, 3, 3], "m": 0.0038091078096506546, "b": -0.03501956985921273, "offset": 2952}, "econv1a.bias": {"shape": [16], "m": 0.0015229881511565468, "b": -0.08972057696648256, "offset": 4680}, "econv1b.weight": {"shape": [16, 16, 3, 3], "m": 0.003541980283788085, "b": -0.024257613173459314, "offset": 4696}, "econv1b.bias": {"shape": [16], "m": 0.0007493317922580185, "b": -0.01717703189449371, "offset": 7000}, "econv2a.weight": {"shape": [24, 16, 3, 3], "m": 0.004257149935891527, "b": -0.10830029475632796, "offset": 7016}, "econv2a.bias": {"shape": [24], "m": 0.0018469851282739563, "b": -0.039043150292460915, "offset": 10472}, "econv2b.weight": {"shape": [24, 24, 3, 3], "m": 0.003692882261246302, "b": 0.014570155243858629, "offset": 10496}, "econv2b.bias": {"shape": [24], "m": 0.0015773928287077922, "b": -0.09682781218874212, "offset": 15680}, "econv3a.weight": {"shape": [32, 24, 3, 3], "m": 0.004739632224737194, "b": -0.08096737165466028, "offset": 15704}, "econv3a.bias": {"shape": [32], "m": 0.003417840277194228, "b": -0.17800170646357572, "offset": 22616}, "econv3b.weight": {"shape": [32, 32, 3, 3], "m": 0.006169313725721518, "b": -0.1392947462514581, "offset": 22648}, "econv3b.bias": {"shape": [32], "m": 0.002395098493836551, "b": 0.04863378658212336, "offset": 31864}, "dconv2a.weight": {"shape": [32, 24, 4, 4], "m": 0.006525508556485737, "b": -0.1891104618739483, "offset": 31896}, "dconv2a.bias": {"shape": [24], "m": 0.001945158984260619, "b": -0.09846048496862408, "offset": 44184}, "dconv2b.weight": {"shape": [24, 48, 1, 1], "m": 0.005023270798627772, "b": -0.02599899473145395, "offset": 44208}, "dconv2b.bias": {"shape": [24], "m": 0.0023881538977241217, "b": -0.11235455948760031, "offset": 45360}, "dconv1a.weight": {"shape": [24, 16, 4, 4], "m": 0.006541182145219021, "b": -0.05923405808968296, "offset": 45384}, "dconv1a.bias": {"shape": [16], "m": 0.0014837178601949143, "b": 0.02937670514057719, "offset": 51528}, "dconv1b.weight": {"shape": [16, 32, 1, 1], "m": 0.006318184981353616, "b": -0.06707240479707344, "offset": 51544}, "dconv1b.bias": {"shape": [16], "m": 0.0018300923110925983, "b": -0.04866552788009823, "offset": 52056}, "dconv0a.weight": {"shape": [16, 12, 4, 4], "m": 0.006492181887244879, "b": -0.10991779751081476, "offset": 52072}, "dconv0a.bias": {"shape": [12], "m": 0.0006236774580819266, "b": -0.0071538367441722345, "offset": 55144}, "dconv0b.weight": {"shape": [12, 24, 1, 1], "m": 0.007381430787605992, "b": -0.22328150693438298, "offset": 55156}, "dconv0b.bias": {"shape": [12], "m": 0.0014362952071044657, "b": -0.058943032177407845, "offset": 55444}, "convo.weight": {"shape": [3, 12, 3, 3], "m": 0.0019896388334606657, "b": 0.02535569967242951, "offset": 55456}, "convo.bias": {"shape": [3], "m": 0.00011152948956100307, "b": 0.015272807389288684, "offset": 55780}}}