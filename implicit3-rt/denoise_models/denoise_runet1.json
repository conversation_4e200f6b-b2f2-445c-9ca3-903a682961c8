{"dtype": "int8", "state_dict": {"econv0a.weight": {"shape": [12, 3, 3, 3], "m": 0.003129418828236626, "b": -0.06027668906343603, "offset": 0}, "econv0a.bias": {"shape": [12], "m": 0.000501980002109821, "b": 0.10279986119041075, "offset": 324}, "econv0b.weight": {"shape": [12, 12, 3, 3], "m": 0.002293820892061506, "b": 0.009666530149323616, "offset": 336}, "econv0b.bias": {"shape": [12], "m": 0.000694883487288207, "b": -0.00994715957757629, "offset": 1632}, "econv1.weight": {"shape": [16, 12, 3, 3], "m": 0.0014458493778432369, "b": -0.01122211434496817, "offset": 1644}, "econv1.bias": {"shape": [16], "m": 0.0005317464166758011, "b": -0.012029909357732668, "offset": 3372}, "econv2.weight": {"shape": [24, 16, 3, 3], "m": 0.0017925673508007822, "b": 0.00027159719583191855, "offset": 3388}, "econv2.bias": {"shape": [24], "m": 0.0006654884321161866, "b": -0.004333353246119179, "offset": 6844}, "mconv1.weight": {"shape": [32, 24, 3, 3], "m": 0.002058070486344198, "b": 0.028349688443321586, "offset": 6868}, "mconv1.bias": {"shape": [32], "m": 0.0005228809275469937, "b": 0.014651044793836374, "offset": 13780}, "mconv2.weight": {"shape": [32, 32, 3, 3], "m": 0.0031970782875078998, "b": -0.09663262745742712, "offset": 13812}, "mconv2.bias": {"shape": [32], "m": 0.0005115922452703563, "b": -0.0013812305449597273, "offset": 23028}, "dconv2a.weight": {"shape": [32, 24, 4, 4], "m": 0.002434392177515913, "b": 0.002798149686780438, "offset": 23060}, "dconv2a.bias": {"shape": [24], "m": 0.0004493347493408239, "b": 0.01284179720717659, "offset": 35348}, "dconv2b.weight": {"shape": [24, 48, 3, 3], "m": 0.0019592474824405353, "b": -0.01570675616829509, "offset": 35372}, "dconv2b.bias": {"shape": [24], "m": 0.0004126273060518681, "b": 0.0136595550906995, "offset": 45740}, "dconv1a.weight": {"shape": [24, 16, 4, 4], "m": 0.003772808861695054, "b": -0.12072040947677581, "offset": 45764}, "dconv1a.bias": {"shape": [16], "m": 0.00038102751647471633, "b": 0.003020944623525139, "offset": 51908}, "dconv1b.weight": {"shape": [16, 32, 3, 3], "m": 0.0023291801545459023, "b": -0.05896752600205746, "offset": 51924}, "dconv1b.bias": {"shape": [16], "m": 0.0006190103769676651, "b": -0.008637226498108275, "offset": 56532}, "dconv0a.weight": {"shape": [16, 12, 4, 4], "m": 0.005499835197742169, "b": -0.06751424303421605, "offset": 56548}, "dconv0a.bias": {"shape": [12], "m": 0.0004713506886685849, "b": 0.01409463814841335, "offset": 59620}, "dconv0b.weight": {"shape": [12, 24, 3, 3], "m": 0.0030018503849322978, "b": -0.00047785960710966524, "offset": 59632}, "dconv0b.bias": {"shape": [12], "m": 0.0003798760391853668, "b": 0.016501233350117127, "offset": 62224}, "dconv0.weight": {"shape": [3, 12, 3, 3], "m": 0.002712630776445757, "b": 0.14140845514728656, "offset": 62236}, "dconv0.bias": {"shape": [3], "m": 7.496201513653834e-05, "b": -0.0049836552465039215, "offset": 62560}}}