{"dtype": "int8", "state_dict": {"convi.weight": {"shape": [12, 3, 3, 3], "m": 0.0029375370200050866, "b": 0.08863501826204723, "offset": 0}, "convi.bias": {"shape": [12], "m": 0.0007484340382333453, "b": 0.05450698776606301, "offset": 324}, "econv0a.weight": {"shape": [12, 12, 3, 3], "m": 0.004382926029163402, "b": -0.19886978576471515, "offset": 336}, "econv0a.bias": {"shape": [12], "m": 0.0010650084475333126, "b": -0.018556509114322328, "offset": 1632}, "econv0b.weight": {"shape": [12, 12, 3, 3], "m": 0.00333730827321063, "b": 0.0014338416385126784, "offset": 1644}, "econv0b.bias": {"shape": [12], "m": 0.0009935065004477509, "b": 0.027857624684435617, "offset": 2940}, "econv1a.weight": {"shape": [16, 12, 3, 3], "m": 0.003903430990371824, "b": 0.0037849457522202146, "offset": 2952}, "econv1a.bias": {"shape": [16], "m": 0.001678933949448063, "b": -0.1270009923720472, "offset": 4680}, "econv1b.weight": {"shape": [16, 16, 3, 3], "m": 0.004166002550529348, "b": -0.01640951858023454, "offset": 4696}, "econv1b.bias": {"shape": [16], "m": 0.000680191398509069, "b": -0.05836099481540534, "offset": 7000}, "econv2a.weight": {"shape": [24, 16, 3, 3], "m": 0.005271716432257012, "b": 0.017213723816714377, "offset": 7016}, "econv2a.bias": {"shape": [24], "m": 0.0019303430895228962, "b": -0.10913955322006247, "offset": 10472}, "econv2b.weight": {"shape": [24, 24, 3, 3], "m": 0.004611924828689551, "b": -0.04920180860082246, "offset": 10496}, "econv2b.bias": {"shape": [24], "m": 0.0013358694511455492, "b": 0.02614476294307916, "offset": 15680}, "econv3a.weight": {"shape": [32, 24, 3, 3], "m": 0.005956680003664744, "b": -0.10298280396865722, "offset": 15704}, "econv3a.bias": {"shape": [32], "m": 0.003010284863236935, "b": -0.12614613177263567, "offset": 22616}, "econv3b.weight": {"shape": [32, 32, 3, 3], "m": 0.010211388207679641, "b": -0.17696289224564743, "offset": 22648}, "econv3b.bias": {"shape": [32], "m": 0.0034600213240229727, "b": 0.03753714338295877, "offset": 31864}, "dconv2a.weight": {"shape": [32, 24, 4, 4], "m": 0.007638294056783104, "b": -0.2896209565024923, "offset": 31896}, "dconv2a.bias": {"shape": [24], "m": 0.003622617788928644, "b": 0.022006763253518857, "offset": 44184}, "dconv2b.weight": {"shape": [24, 48, 1, 1], "m": 0.004269340348954852, "b": 0.05989201799666677, "offset": 44208}, "dconv2b.bias": {"shape": [24], "m": 0.002504259207559343, "b": -0.10221065587862693, "offset": 45360}, "dconv1a.weight": {"shape": [24, 16, 4, 4], "m": 0.009776453956891452, "b": -0.36952216387543624, "offset": 45384}, "dconv1a.bias": {"shape": [16], "m": 0.0021278921345152324, "b": 0.13814018982545923, "offset": 51528}, "dconv1b.weight": {"shape": [16, 32, 1, 1], "m": 0.005121674605029718, "b": -0.015064256157568234, "offset": 51544}, "dconv1b.bias": {"shape": [16], "m": 0.0011419939985454924, "b": 0.017517685293683444, "offset": 52056}, "dconv0a.weight": {"shape": [16, 12, 4, 4], "m": 0.005914105257396608, "b": -0.052365475256738736, "offset": 52072}, "dconv0a.bias": {"shape": [12], "m": 0.0006343864103314266, "b": 0.02619864382843956, "offset": 55144}, "dconv0b.weight": {"shape": [12, 24, 1, 1], "m": 0.005668499499699759, "b": -0.09005571483068597, "offset": 55156}, "dconv0b.bias": {"shape": [12], "m": 0.0009067165935806801, "b": 0.016591084901728836, "offset": 55444}, "convo.weight": {"shape": [3, 12, 3, 3], "m": 0.0019021493673698868, "b": 0.04181558700801813, "offset": 55456}, "convo.bias": {"shape": [3], "m": 0.00011521122554314192, "b": -0.000985058438284338, "offset": 55780}}}