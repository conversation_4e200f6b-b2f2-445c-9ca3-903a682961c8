{"dtype": "int8", "state_dict": {"convi.weight": {"shape": [12, 3, 3, 3], "m": 0.0023123930724486823, "b": 0.012028769196670508, "offset": 0}, "convi.bias": {"shape": [12], "m": 0.0015205622093643835, "b": 0.05166124703371358, "offset": 324}, "econv0a.weight": {"shape": [12, 12, 3, 3], "m": 0.0038777841503616402, "b": 0.15146435565626604, "offset": 336}, "econv0a.bias": {"shape": [12], "m": 0.0009801973891407953, "b": 0.026592172185190055, "offset": 1632}, "econv0b.weight": {"shape": [12, 12, 3, 3], "m": 0.003548772593308094, "b": -0.009889944662666017, "offset": 1644}, "econv0b.bias": {"shape": [12], "m": 0.0006683556190465271, "b": -0.009558263522816982, "offset": 2940}, "econv1a.weight": {"shape": [16, 12, 3, 3], "m": 0.004567983947705998, "b": 0.0575559155158758, "offset": 2952}, "econv1a.bias": {"shape": [16], "m": 0.000964752347259731, "b": -0.007000279172763732, "offset": 4680}, "econv1b.weight": {"shape": [16, 16, 3, 3], "m": 0.004299741524916428, "b": 0.02973835743390596, "offset": 4696}, "econv1b.bias": {"shape": [16], "m": 0.000940024899538121, "b": -0.03654286263033958, "offset": 7000}, "econv2a.weight": {"shape": [24, 16, 3, 3], "m": 0.0040977987427165035, "b": -0.022705088417795727, "offset": 7016}, "econv2a.bias": {"shape": [24], "m": 0.001551748912787325, "b": -0.007272376814854209, "offset": 10472}, "econv2b.weight": {"shape": [24, 24, 3, 3], "m": 0.00421278611457142, "b": -0.08712682656628001, "offset": 10496}, "econv2b.bias": {"shape": [24], "m": 0.0016930685312819068, "b": -0.06490114401423577, "offset": 15680}, "econv3a.weight": {"shape": [32, 24, 3, 3], "m": 0.0036175149014830775, "b": 0.04482626405016782, "offset": 15704}, "econv3a.bias": {"shape": [32], "m": 0.002075404256254762, "b": -0.010167106822296823, "offset": 22616}, "econv3b.weight": {"shape": [32, 32, 3, 3], "m": 0.005305903953510326, "b": -0.0876851589470119, "offset": 22648}, "econv3b.bias": {"shape": [32], "m": 0.0021748785134200212, "b": 0.18575624843220134, "offset": 31864}, "dconv2a.weight": {"shape": [32, 24, 4, 4], "m": 0.0074261796530599306, "b": -0.24869046747403767, "offset": 31896}, "dconv2a.bias": {"shape": [24], "m": 0.0023675711132480734, "b": 0.19233863061554202, "offset": 44184}, "dconv2b.weight": {"shape": [24, 48, 1, 1], "m": 0.004680088868882255, "b": -0.0796291571303478, "offset": 44208}, "dconv2b.bias": {"shape": [24], "m": 0.0016710819498148967, "b": -0.030629214560218276, "offset": 45360}, "dconv1a.weight": {"shape": [24, 16, 4, 4], "m": 0.006467186300496291, "b": -0.3696718996323447, "offset": 45384}, "dconv1a.bias": {"shape": [16], "m": 0.0005965632513703506, "b": 0.02214363353031583, "offset": 51528}, "dconv1b.weight": {"shape": [16, 32, 1, 1], "m": 0.00394519728431522, "b": -0.03006585159893127, "offset": 51544}, "dconv1b.bias": {"shape": [16], "m": 0.0008910744680527428, "b": 0.042258739436346476, "offset": 52056}, "dconv0a.weight": {"shape": [16, 12, 4, 4], "m": 0.005521172631292178, "b": -0.2409045724329807, "offset": 52072}, "dconv0a.bias": {"shape": [12], "m": 0.0002979788654359581, "b": 0.0108142329242484, "offset": 55144}, "dconv0b.weight": {"shape": [12, 24, 1, 1], "m": 0.004091942141905684, "b": -0.004758256758026658, "offset": 55156}, "dconv0b.bias": {"shape": [12], "m": 0.0013464557085905568, "b": 0.03048718804888298, "offset": 55444}, "convo.weight": {"shape": [3, 12, 3, 3], "m": 0.0016643843808016934, "b": -0.010094158760793892, "offset": 55456}, "convo.bias": {"shape": [3], "m": 0.00011157291229328892, "b": -0.025891277705154016, "offset": 55780}}}