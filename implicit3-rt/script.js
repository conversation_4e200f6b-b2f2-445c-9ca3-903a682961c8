// 3D Implicit Surface Grapher

const NAME = "spirulae.implicit3-rt.";

const builtinStates = [
    {
        "name": "<PERSON> Flower",
        "id": "red-flower",
        "state": {"input":"a=atan2(z,x)+pi/2\n(x^2+z^2)^2+16y^2=2(x^2+z^2)(sin(2.5a)^2+0.5sin(10a)^2)","params":{"cLatex":true,"cAutoUpdate":true,"fBasic":false,"sStep":"0.01","bLight":true,"bYup":false,"sSpp":"256","sLightPathDepth":"64","cMis":false,"sClip":"1","cClosed":true,"sDenoise":"null","sTonemap":"None","rScale1":0.5,"rScale2":0.5,"fAppearance1":true,"sColor":"2","sField":"0","bGrid":false,"bDiscontinuity":true,"rOpacity":0,"rIor":1.7009999999999998,"rRoughness1":0,"rMetallic1":1,"rTint1":1,"rEmission1":0,"rAbsorb1":0.49995,"rVEmission1":0,"rScatter1":0,"rScatterAniso1":0,"fAppearance2":true,"rBrightness2":0.875,"rContrast2":0.2,"rRoughness2":0.3,"rMetallic2":1,"rIor2":0.9990000000000001,"rTint2":1,"rEmission2":0,"rAbsorb2":0,"rVDecayAbs":0.5004000000000001,"rVAbsorbHue":0,"rVAbsorbChr":0.4,"rVEmission2":0,"rScatter2":0,"rScatterAniso2":0,"rVDecaySca":0.01,"rVSharpSca":0,"fLighting":true,"rTheta":-0.546637121724624,"rPhi":2.356194490192345,"rLightIntensity":0.5,"rLightSky":0.2,"rLightAmbient":0,"rLightSoftness":0.8002,"rLightHardness":0,"fCamera":true,"fov":0.7863406411935253,"rExposure":0.5,"ry":0,"rCameraDistortion":0.5,"rFocalLength":0.5,"rApertureSize":0.28538,"rApertureShape":0,"rApertureRotate":0},"state":{"name":"spirulae.implicit3-rt.state","iFrame":177,"width":924,"height":595,"screenCenter":{"x":0.32812050823525435,"y":0.5},"defaultScreenCenter":true,"worldSpace":true,"iTime":-1,"rz":-3.054513020910302,"rx":-1.5379644737231006,"ry":0,"fov":0.7863406411935253,"scale":0.5344202287234547,"clipSize":[2,2,2],"rTheta":-0.546637121724624,"rPhi":2.356194490192345,"renderNeeded":false,"cLatex":true,"cAutoUpdate":true,"fBasic":false,"sStep":"0.01","bLight":true,"bYup":false,"sSpp":256,"sClip":"1","cClosed":true,"sDenoise":"null","rScale1":0.5,"rScale2":0.5,"sColor":"2","sField":"0","bGrid":false,"bDiscontinuity":true,"fAppearance":false,"rOpacity":0,"rIor":1.7009999999999998,"rRoughness1":0,"rRoughness2":0.3,"rEmission1":0,"rEmission2":0,"rAbsorb1":0.49995,"rAbsorb2":0,"rScatter1":0,"rScatter2":0,"rVDecayAbs":0.5004000000000001,"rVDecaySca":0.01,"rVAbsorbHue":0,"rVAbsorbChr":0.4,"rVAbsorbBri":0.97,"fLighting":true,"rLightIntensity":0.5,"rLightSky":0.2,"rLightAmbient":0,"rLightSoftness":0.8002,"rLightHardness":0,"fCamera":true,"rExposure":0.5,"rCameraDistortion":0.5,"rFocalLength":0.5,"rApertureSize":0.28538,"rApertureShape":0,"rApertureRotate":0,"sLightPathDepth":"64","cMis":false,"sTonemap":"None","fAppearance1":null,"rMetallic1":1,"rTint1":1,"rVEmission1":0,"rScatterAniso1":0,"fAppearance2":null,"rBrightness2":0.875,"rContrast2":0.2,"rMetallic2":1,"rIor2":1,"rTint2":1,"rVEmission2":0,"rScatterAniso2":0,"rVSharpSca":0}}
    },
    {
        "name": "Lily Leaf",
        "id": "lily-leaf",
        "state": {"input":"a=atan2(z,x)+pi/2\n(x^2+z^2)^2+16y^2=2(x^2+z^2)(sin(a/2)^2+1/2sin(10a)^2)\nc_hsv=vec3(3atan2(y,x),hypot(x,y),1)","params":{"cLatex":true,"cAutoUpdate":true,"fBasic":false,"sStep":"0.01","bLight":true,"bYup":false,"sSpp":"256","sLightPathDepth":"64","cMis":false,"sClip":"1","cClosed":true,"sDenoise":"null","sTonemap":"None","rScale1":0.5,"rScale2":0.5,"fAppearance1":true,"sColor":"2","sField":"0","bGrid":false,"bDiscontinuity":true,"rOpacity":0,"rIor":1.7009999999999998,"rRoughness1":0,"rMetallic1":1,"rTint1":1,"rEmission1":0,"rAbsorb1":0.49995,"rVEmission1":0,"rScatter1":0,"rScatterAniso1":0,"fAppearance2":true,"rBrightness2":0.875,"rContrast2":0.2,"rRoughness2":0.3,"rMetallic2":1,"rIor2":0.9990000000000001,"rTint2":1,"rEmission2":0,"rAbsorb2":0,"rVDecayAbs":0.5004000000000001,"rVAbsorbHue":0,"rVAbsorbChr":0.4,"rVEmission2":0,"rScatter2":0,"rScatterAniso2":0,"rVDecaySca":0.01,"rVSharpSca":0,"fLighting":true,"rTheta":3.2421236185046665,"rPhi":2.356194490192345,"rLightIntensity":0.5,"rLightSky":0.2,"rLightAmbient":0,"rLightSoftness":0.8002,"rLightHardness":0,"fCamera":true,"fov":0.7863406411935253,"rExposure":0.5,"ry":0,"rCameraDistortion":0.5,"rFocalLength":0.5,"rApertureSize":0.6764,"rApertureShape":0,"rApertureRotate":0},"state":{"name":"spirulae.implicit3-rt.state","iFrame":252,"width":1848,"height":600,"screenCenter":{"x":0.39062050823525424,"y":0.3915159038822141},"defaultScreenCenter":false,"worldSpace":true,"iTime":-1,"rz":-7.044513020910296,"rx":-1.7779644737231008,"ry":0,"fov":0.7863406411935253,"scale":0.7478350817011133,"clipSize":[2,2,2],"rTheta":3.2421236185046665,"rPhi":2.356194490192345,"renderNeeded":false,"cLatex":true,"cAutoUpdate":true,"fBasic":false,"sStep":"0.01","bLight":true,"bYup":false,"sSpp":256,"sClip":"1","cClosed":true,"sDenoise":"null","rScale1":0.5,"rScale2":0.5,"sColor":"2","sField":"0","bGrid":false,"bDiscontinuity":true,"fAppearance":false,"rOpacity":0,"rIor":1.7009999999999998,"rRoughness1":0,"rRoughness2":0.3,"rEmission1":0,"rEmission2":0,"rAbsorb1":0.49995,"rAbsorb2":0,"rScatter1":0,"rScatter2":0,"rVDecayAbs":0.5004000000000001,"rVDecaySca":0.01,"rVAbsorbHue":0,"rVAbsorbChr":0.4,"rVAbsorbBri":0.97,"fLighting":true,"rLightIntensity":0.5,"rLightSky":0.2,"rLightAmbient":0,"rLightSoftness":0.8002,"rLightHardness":0,"fCamera":true,"rExposure":0.5,"rCameraDistortion":0.5,"rFocalLength":0.5,"rApertureSize":0.6764,"rApertureShape":0,"rApertureRotate":0,"sLightPathDepth":"64","cMis":false,"sTonemap":"None","fAppearance1":true,"rMetallic1":1,"rTint1":1,"rVEmission1":0,"rScatterAniso1":0,"fAppearance2":true,"rBrightness2":0.875,"rContrast2":0.2,"rMetallic2":1,"rIor2":0.9990000000000001,"rTint2":1,"rVEmission2":0,"rScatterAniso2":0,"rVSharpSca":0}}
    },
    {
        "name": "Spheres",
        "id": "spheres",
        "state": {"input":"sin(x)sin(y)sin(z)+0.8\ns(x)=1/(1+exp(-x))\nc_rgb=vec3(s(x),s(y),s(z))","params":{"cLatex":true,"cAutoUpdate":true,"fBasic":false,"sStep":"0.01","bLight":true,"bYup":false,"sSpp":"256","sLightPathDepth":"64","cMis":false,"sClip":"1","cClosed":true,"sDenoise":"null","sTonemap":"Aces","rScale1":0.74892,"rScale2":0.5,"fAppearance1":false,"sColor":"2","sField":"0","bGrid":false,"bDiscontinuity":true,"rOpacity":1,"rIor":3,"rRoughness1":0,"rMetallic1":0,"rTint1":1,"rEmission1":0,"rAbsorb1":0.49995,"rVEmission1":0,"rScatter1":0,"rScatterAniso1":0,"fAppearance2":false,"rBrightness2":0.572,"rContrast2":0.2,"rRoughness2":0,"rMetallic2":0,"rIor2":3,"rTint2":0,"rEmission2":0,"rAbsorb2":0.49401,"rVDecayAbs":0.5004000000000001,"rVAbsorbHue":0.564,"rVAbsorbChr":0.4,"rVEmission2":0.093,"rScatter2":0,"rScatterAniso2":0,"rVDecaySca":0.2525,"rVSharpSca":0,"fLighting":true,"rTheta":3.1667253948185117,"rPhi":2.767743127812608,"rLightIntensity":0.5,"rLightSky":0,"rLightAmbient":0,"rLightSoftness":0.8002,"rLightHardness":0,"fCamera":true,"fov":0.7863406411935253,"rExposure":0.5,"ry":0,"rCameraDistortion":0.5,"rFocalLength":0.5,"rApertureSize":0.36769999999999997,"rApertureShape":0,"rApertureRotate":0},"state":{"name":"spirulae.implicit3-rt.state","iFrame":84,"width":924,"height":600,"screenCenter":{"x":0.32812050823525435,"y":0.5},"defaultScreenCenter":true,"worldSpace":true,"iTime":-1,"rz":-9.064513020910269,"rx":-1.717964473723101,"ry":0,"fov":0.7863406411935253,"scale":0.40100844007602443,"clipSize":[2,2,2],"rTheta":3.1667253948185117,"rPhi":2.767743127812608,"renderNeeded":false,"cLatex":true,"cAutoUpdate":true,"fBasic":false,"sStep":"0.01","bLight":true,"bYup":false,"sSpp":256,"sClip":"1","cClosed":true,"sDenoise":"null","rScale1":0.74892,"rScale2":0.5,"sColor":"2","sField":"0","bGrid":false,"bDiscontinuity":true,"fAppearance":false,"rOpacity":1,"rIor":3,"rRoughness1":0,"rRoughness2":0,"rEmission1":0,"rEmission2":0,"rAbsorb1":0.49995,"rAbsorb2":0.49401,"rScatter1":0,"rScatter2":0,"rVDecayAbs":0.5004000000000001,"rVDecaySca":0.2525,"rVAbsorbHue":0.564,"rVAbsorbChr":0.4,"rVAbsorbBri":0.97,"fLighting":true,"rLightIntensity":0.5,"rLightSky":0,"rLightAmbient":0,"rLightSoftness":0.8002,"rLightHardness":0,"fCamera":true,"rExposure":0.5,"rCameraDistortion":0.5,"rFocalLength":0.5,"rApertureSize":0.36769999999999997,"rApertureShape":0,"rApertureRotate":0,"sLightPathDepth":"64","cMis":false,"sTonemap":"Aces","fAppearance1":false,"rMetallic1":0,"rTint1":1,"rVEmission1":0,"rScatterAniso1":0,"fAppearance2":false,"rBrightness2":0.572,"rContrast2":0.2,"rMetallic2":0,"rIor2":3,"rTint2":0,"rVEmission2":0.093,"rScatterAniso2":0,"rVSharpSca":0}}
    },
    {
        "name": "Burning Ship",
        "id": "burning-ship",
        "state": {"input":"c=-vec2(x,y)-vec2(1/4,1/2)\nf(z)=vec2(z.x^2-z.y^2,2|z.xz.y|)+c\ng=f(f(f(f(f(f(c\nz=0.8|g|^-0.4-1","params":{"cLatex":true,"cAutoUpdate":true,"fBasic":false,"sStep":"0.01","bLight":false,"bYup":false,"sSpp":"256","sLightPathDepth":"64","cMis":false,"sClip":"2","cClosed":true,"sDenoise":"null","sTonemap":"None","rScale1":0.5,"rScale2":0.5,"fAppearance1":true,"sColor":"1","sField":"0","bGrid":false,"bDiscontinuity":true,"rOpacity":0,"rIor":1.7009999999999998,"rRoughness1":0,"rMetallic1":1,"rTint1":1,"rEmission1":0,"rAbsorb1":0.49995,"rVEmission1":0,"rScatter1":0,"rScatterAniso1":0,"fAppearance2":true,"rBrightness2":0.875,"rContrast2":0.2,"rRoughness2":0.57,"rMetallic2":1,"rIor2":0.9990000000000001,"rTint2":1,"rEmission2":0,"rAbsorb2":0,"rVDecayAbs":0.5496800000000001,"rVAbsorbHue":0.142,"rVAbsorbChr":0.4,"rVEmission2":0,"rScatter2":0,"rScatterAniso2":0,"rVDecaySca":0.08663,"rVSharpSca":0,"fLighting":true,"rTheta":-1.4451326206513049,"rPhi":2.6734953482049137,"rLightIntensity":0.5,"rLightSky":0,"rLightAmbient":0,"rLightSoftness":0.517483,"rLightHardness":0,"fCamera":true,"fov":1.1442308762904747,"rExposure":0.5,"ry":0,"rCameraDistortion":0.5,"rFocalLength":0.5,"rApertureSize":0.01,"rApertureShape":0,"rApertureRotate":0},"state":{"name":"spirulae.implicit3-rt.state","iFrame":25,"width":924,"height":595,"screenCenter":{"x":0.32812050823525435,"y":0.5},"defaultScreenCenter":true,"worldSpace":true,"iTime":-1,"rz":-2.914513020910296,"rx":-1.6979644737231008,"ry":0,"fov":1.1442308762904747,"scale":0.5336725638907442,"clipSize":[2,2,2],"rTheta":-1.4451326206513049,"rPhi":2.6734953482049137,"renderNeeded":false,"cLatex":true,"cAutoUpdate":true,"fBasic":false,"sStep":"0.01","bLight":false,"bYup":false,"sSpp":256,"sClip":"2","cClosed":true,"sDenoise":"null","rScale1":0.5,"rScale2":0.5,"sColor":"1","sField":"0","bGrid":false,"bDiscontinuity":true,"fAppearance":false,"rOpacity":0,"rIor":1.7009999999999998,"rRoughness1":0,"rRoughness2":0.57,"rEmission1":0,"rEmission2":0,"rAbsorb1":0.49995,"rAbsorb2":0,"rScatter1":0,"rScatter2":0,"rVDecayAbs":0.5496800000000001,"rVDecaySca":0.08663,"rVAbsorbHue":0.142,"rVAbsorbChr":0.4,"rVAbsorbBri":0.97,"fLighting":true,"rLightIntensity":0.5,"rLightSky":0,"rLightAmbient":0,"rLightSoftness":0.517483,"rLightHardness":0,"fCamera":true,"rExposure":0.5,"rCameraDistortion":0.5,"rFocalLength":0.5,"rApertureSize":0.01,"rApertureShape":0,"rApertureRotate":0,"sLightPathDepth":"64","cMis":false,"sTonemap":"None","fAppearance1":null,"rMetallic1":1,"rTint1":1,"rVEmission1":0,"rScatterAniso1":0,"fAppearance2":null,"rBrightness2":0.875,"rContrast2":0.2,"rMetallic2":1,"rIor2":1,"rTint2":1,"rVEmission2":0,"rScatterAniso2":0,"rVSharpSca":0}}
    },
    {
        "name": "Gamma Function",
        "id": "gamma-function",
        "state": {"input":"c=gamma(x+yi)\nz=|c|\nc_hsv=vec3(arg(c),1,1)","params":{"cLatex":true,"cAutoUpdate":true,"fBasic":false,"sStep":"0.01","bLight":true,"bYup":false,"sSpp":"256","sLightPathDepth":"64","cMis":false,"sClip":"2","cClosed":true,"sDenoise":"null","sTonemap":"None","rScale1":0.6783600000000001,"rScale2":0.5,"fAppearance1":true,"sColor":"2","sField":"0","bGrid":false,"bDiscontinuity":true,"rOpacity":1,"rIor":3,"rRoughness1":0,"rMetallic1":0,"rTint1":0,"rEmission1":0,"rAbsorb1":0.49995,"rVEmission1":0,"rScatter1":0,"rScatterAniso1":0,"fAppearance2":true,"rBrightness2":0.875,"rContrast2":0.2,"rRoughness2":0.3,"rMetallic2":1,"rIor2":0.9990000000000001,"rTint2":1,"rEmission2":0,"rAbsorb2":0,"rVDecayAbs":0.5496800000000001,"rVAbsorbHue":0.142,"rVAbsorbChr":0.4,"rVEmission2":0,"rScatter2":0,"rScatterAniso2":0,"rVDecaySca":0.08663,"rVSharpSca":0,"fLighting":true,"rTheta":3.0410616886749198,"rPhi":2.305929007734908,"rLightIntensity":0.5,"rLightSky":0,"rLightAmbient":0,"rLightSoftness":0.649351,"rLightHardness":0,"fCamera":true,"fov":0.7863406411935253,"rExposure":0.5,"ry":0,"rCameraDistortion":0.5,"rFocalLength":0.5,"rApertureSize":0.45982,"rApertureShape":0,"rApertureRotate":0},"state":{"name":"spirulae.implicit3-rt.state","iFrame":256,"width":924,"height":600,"screenCenter":{"x":0.32812050823525435,"y":0.5},"defaultScreenCenter":true,"worldSpace":true,"iTime":-1,"rz":-6.754513020910291,"rx":-1.3079644737231004,"ry":0,"fov":0.7863406411935253,"scale":0.48230778804133084,"clipSize":[2,2,2],"rTheta":3.0410616886749198,"rPhi":2.305929007734908,"renderNeeded":false,"cLatex":true,"cAutoUpdate":true,"fBasic":false,"sStep":"0.01","bLight":true,"bYup":false,"sSpp":256,"sLightPathDepth":"64","cMis":false,"sClip":"2","cClosed":true,"sDenoise":"null","sTonemap":"None","rScale1":0.6783600000000001,"rScale2":0.5,"fAppearance1":true,"sColor":"2","sField":"0","bGrid":false,"bDiscontinuity":true,"rOpacity":1,"rIor":3,"rRoughness1":0,"rMetallic1":0,"rTint1":0,"rEmission1":0,"rAbsorb1":0.49995,"rVEmission1":0,"rScatter1":0,"rScatterAniso1":0,"fAppearance2":true,"rBrightness2":0.875,"rContrast2":0.2,"rRoughness2":0.3,"rMetallic2":1,"rIor2":0.9990000000000001,"rTint2":1,"rEmission2":0,"rAbsorb2":0,"rVDecayAbs":0.5496800000000001,"rVAbsorbHue":0.142,"rVAbsorbChr":0.4,"rVEmission2":0,"rScatter2":0,"rScatterAniso2":0,"rVDecaySca":0.08663,"rVSharpSca":0,"fLighting":true,"rLightIntensity":0.5,"rLightSky":0,"rLightAmbient":0,"rLightSoftness":0.649351,"rLightHardness":0,"fCamera":true,"rExposure":0.5,"rCameraDistortion":0.5,"rFocalLength":0.5,"rApertureSize":0.45982,"rApertureShape":0,"rApertureRotate":0}}
    },
    {
        "name": "Fractal Sponge",
        "id": "fractal-sponge",
        "state": {"input":"w(x,y)=sqrt(x^2-|x|y+y^2)\nf0(x,y,z)=min(w(x,y),w(x,z),w(y,z))-1\nr(x)=6/piasin(sin(pi/6x)\nf(n)=f0(r(nx),r(ny),r(nz))\nb=max(|x|,|y|,|z|)-1\nh=min(f(3),f(9),f(27),f(81),f(243))\nmax(5b,-h)","params":{"cLatex":true,"cAutoUpdate":true,"fBasic":false,"sStep":"0.01","bLight":false,"bYup":false,"sSpp":"256","sLightPathDepth":"16","cMis":false,"sClip":"1","cClosed":true,"sDenoise":"null","sTonemap":"None","rScale1":0.33928,"rScale2":0.5,"fAppearance1":true,"sColor":"2","sField":"0","bGrid":false,"bDiscontinuity":true,"rOpacity":1,"rIor":1.7009999999999998,"rRoughness1":0.57,"rMetallic1":1,"rTint1":1,"rEmission1":0,"rAbsorb1":0.49995,"rVEmission1":0,"rScatter1":0,"rScatterAniso1":0,"fAppearance2":true,"rBrightness2":0.875,"rContrast2":0.2,"rRoughness2":0,"rMetallic2":1,"rIor2":0.9990000000000001,"rTint2":1,"rEmission2":0,"rAbsorb2":0,"rVDecayAbs":0.5004000000000001,"rVAbsorbHue":0,"rVAbsorbChr":0.4,"rVEmission2":0,"rScatter2":0,"rScatterAniso2":0,"rVDecaySca":0.01,"rVSharpSca":0,"fLighting":true,"rTheta":-0.7979645340118074,"rPhi":2.3593360828459344,"rLightIntensity":0.5,"rLightSky":0.673,"rLightAmbient":0,"rLightSoftness":0.6783220000000001,"rLightHardness":1,"fCamera":true,"fov":0.7863406411935253,"rExposure":0.5,"ry":0,"rCameraDistortion":0.5,"rFocalLength":0.5,"rApertureSize":0.15209999999999999,"rApertureShape":0,"rApertureRotate":0},"state":{"name":"spirulae.implicit3-rt.state","iFrame":51,"width":924,"height":595,"screenCenter":{"x":0.32812050823525435,"y":0.5},"defaultScreenCenter":true,"worldSpace":true,"iTime":-1,"rz":-4.4045130209102865,"rx":-1.5979644737230845,"ry":0,"fov":0.7863406411935253,"scale":1.7385078636153724,"clipSize":[2,2,2],"rTheta":-0.7979645340118074,"rPhi":2.3593360828459344,"renderNeeded":false,"cLatex":true,"cAutoUpdate":true,"fBasic":false,"sStep":"0.01","bLight":false,"bYup":false,"sSpp":256,"sClip":"1","cClosed":true,"sDenoise":"null","sTonemap":"None","rScale1":0.33928,"rScale2":0.5,"sColor":"2","sField":"0","bGrid":false,"bDiscontinuity":true,"fAppearance":false,"rOpacity":1,"rIor":1.7009999999999998,"rRoughness1":0.57,"rRoughness2":0,"rEmission1":0,"rEmission2":0,"rAbsorb1":0.49995,"rAbsorb2":0,"rScatter1":0,"rScatter2":0,"rVDecayAbs":0.5004000000000001,"rVDecaySca":0.01,"rVAbsorbHue":0,"rVAbsorbChr":0.4,"rVAbsorbBri":0.97,"fLighting":true,"rLightIntensity":0.5,"rLightSky":0.673,"rLightAmbient":0,"rLightSoftness":0.6783220000000001,"rLightHardness":1,"fCamera":true,"rExposure":0.5,"rCameraDistortion":0.5,"rFocalLength":0.5,"rApertureSize":0.15209999999999999,"rApertureShape":0,"rApertureRotate":0,"sLightPathDepth":"16","cMis":false,"fAppearance1":true,"rMetallic1":1,"rTint1":1,"rVEmission1":0,"rScatterAniso1":0,"fAppearance2":true,"rBrightness2":0.875,"rContrast2":0.2,"rMetallic2":1,"rIor2":0.9990000000000001,"rTint2":1,"rVEmission2":0,"rScatterAniso2":0,"rVSharpSca":0}}
    },
    {
        "name": "Womb",
        "id": "womb",
        "state": {"input":"(x^2-1)^2+(y^2-1)^2+(z^2-1)^2+4(x^2y^2+x^2z^2+y^2z^2)+8xyz-2(x^2+y^2+z^2)","params":{"cLatex":true,"cAutoUpdate":true,"fBasic":false,"sStep":"0.04","bLight":false,"bYup":false,"sSpp":"256","sLightPathDepth":"128","cMis":false,"sClip":"1","cClosed":true,"sDenoise":"null","sTonemap":"Aces","rScale1":0.5,"rScale2":0.5,"fAppearance1":false,"sColor":"2","sField":"0","bGrid":false,"bDiscontinuity":true,"rOpacity":0,"rIor":1.7009999999999998,"rRoughness1":0,"rMetallic1":1,"rTint1":1,"rEmission1":0,"rAbsorb1":0.39204,"rVEmission1":0,"rScatter1":0.83556,"rScatterAniso1":-0.99,"fAppearance2":false,"rBrightness2":0.875,"rContrast2":0.2,"rRoughness2":0.687,"rMetallic2":1,"rIor2":0.9990000000000001,"rTint2":1,"rEmission2":0,"rAbsorb2":0,"rVDecayAbs":0.5004000000000001,"rVAbsorbHue":0.551,"rVAbsorbChr":0.4,"rVEmission2":0,"rScatter2":0.69597,"rScatterAniso2":-0.99,"rVDecaySca":0.49985,"rVSharpSca":0,"fLighting":true,"rTheta":3.141592653589793,"rPhi":2.356194490192345,"rLightIntensity":0.5,"rLightSky":0,"rLightAmbient":0,"rLightSoftness":0.52048,"rLightHardness":0,"fCamera":true,"fov":1.6447180019338645,"rExposure":0.5,"ry":0,"rCameraDistortion":0.5,"rFocalLength":0.5,"rApertureSize":0.46178,"rApertureShape":0,"rApertureRotate":0},"state":{"name":"spirulae.implicit3-rt.state","iFrame":32,"width":924,"height":595,"screenCenter":{"x":0.1974386900534362,"y":0.522689075630252},"defaultScreenCenter":false,"worldSpace":true,"iTime":-1,"rz":-2.7045130209103183,"rx":-2.2679644737230915,"ry":0,"fov":1.6447180019338645,"scale":1.2172901450720157,"clipSize":[2,2,2],"rTheta":3.141592653589793,"rPhi":2.356194490192345,"renderNeeded":false,"cLatex":true,"cAutoUpdate":true,"fBasic":false,"sStep":"0.04","bLight":false,"bYup":false,"sSpp":256,"sClip":"1","cClosed":true,"sDenoise":"null","sTonemap":"Aces","rScale1":0.5,"rScale2":0.5,"sColor":"2","sField":"0","bGrid":false,"bDiscontinuity":true,"fAppearance1":false,"rOpacity":0,"rIor":1.7009999999999998,"rRoughness1":0,"rEmission1":0,"rAbsorb1":0.39204,"rVEmission1":0,"rScatter1":0.83556,"rScatterAniso1":-0.99,"fAppearance2":false,"rRoughness2":0.687,"rEmission2":0,"rAbsorb2":0,"rScatter2":0.69597,"rScatterAniso2":-0.99,"rVDecayAbs":0.5004000000000001,"rVDecaySca":0.49985,"rVAbsorbHue":0.551,"rVAbsorbChr":0.4,"fLighting":true,"rLightIntensity":0.5,"rLightSky":0,"rLightAmbient":0,"rLightSoftness":0.52048,"rLightHardness":0,"fCamera":true,"rExposure":0.5,"rCameraDistortion":0.5,"rFocalLength":0.5,"rApertureSize":0.46178,"rApertureShape":0,"rApertureRotate":0,"sLightPathDepth":"128","cMis":false,"rMetallic1":1,"rTint1":1,"rBrightness2":0.875,"rContrast2":0.2,"rMetallic2":1,"rIor2":0.9990000000000001,"rTint2":1,"rVEmission2":0,"rVSharpSca":0}}
    },
    {
        "name": "Complex Sine",
        "id": "complex-sine",
        "state": {"input":"|sin(x+yi)+sin(y+xi)+sin(y+zi)+sin(z+yi)+sin(z+xi)+sin(x+zi)|=1","params":{"cLatex":true,"cAutoUpdate":true,"fBasic":false,"sStep":"0.01","bLight":false,"bYup":false,"sSpp":"256","sLightPathDepth":"64","cMis":false,"sClip":"1","cClosed":true,"sDenoise":"null","sTonemap":"None","rScale1":0.6783600000000001,"rScale2":0.5,"fAppearance1":true,"sColor":"2","sField":"0","bGrid":false,"bDiscontinuity":true,"rOpacity":1,"rIor":1.7009999999999998,"rRoughness1":0,"rMetallic1":1,"rTint1":1,"rEmission1":0,"rAbsorb1":0.49995,"rVEmission1":0,"rScatter1":0,"rScatterAniso1":0,"fAppearance2":true,"rBrightness2":0.875,"rContrast2":0.2,"rRoughness2":0.3,"rMetallic2":1,"rIor2":0.9990000000000001,"rTint2":1,"rEmission2":0,"rAbsorb2":0,"rVDecayAbs":0.5496800000000001,"rVAbsorbHue":0.142,"rVAbsorbChr":0.4,"rVEmission2":0,"rScatter2":0,"rScatterAniso2":0,"rVDecaySca":0.08663,"rVSharpSca":0,"fLighting":true,"rTheta":3.0410616886749198,"rPhi":2.305929007734908,"rLightIntensity":0.5,"rLightSky":0,"rLightAmbient":0,"rLightSoftness":0.649351,"rLightHardness":0,"fCamera":true,"fov":0.7863406411935253,"rExposure":0.5,"ry":0,"rCameraDistortion":0.5,"rFocalLength":0.5,"rApertureSize":0.45982,"rApertureShape":0,"rApertureRotate":0},"state":{"name":"spirulae.implicit3-rt.state","iFrame":116,"width":924,"height":595,"screenCenter":{"x":0.32812050823525435,"y":0.5},"defaultScreenCenter":true,"worldSpace":true,"iTime":-1,"rz":-3.0345130209103024,"rx":-1.6079644737231007,"ry":0,"fov":0.7863406411935253,"scale":0.3446682894493678,"clipSize":[2,2,2],"rTheta":3.0410616886749198,"rPhi":2.305929007734908,"renderNeeded":false,"cLatex":true,"cAutoUpdate":true,"fBasic":false,"sStep":"0.01","bLight":false,"bYup":false,"sSpp":256,"sClip":"1","cClosed":true,"sDenoise":"null","rScale1":0.6783600000000001,"rScale2":0.5,"sColor":"2","sField":"0","bGrid":false,"bDiscontinuity":true,"fAppearance":false,"rOpacity":1,"rIor":1.7009999999999998,"rRoughness1":0,"rRoughness2":0.3,"rEmission1":0,"rEmission2":0,"rAbsorb1":0.49995,"rAbsorb2":0,"rScatter1":0,"rScatter2":0,"rVDecayAbs":0.5496800000000001,"rVDecaySca":0.08663,"rVAbsorbHue":0.142,"rVAbsorbChr":0.4,"rVAbsorbBri":0.97,"fLighting":true,"rLightIntensity":0.5,"rLightSky":0,"rLightAmbient":0,"rLightSoftness":0.649351,"rLightHardness":0,"fCamera":true,"rExposure":0.5,"rCameraDistortion":0.5,"rFocalLength":0.5,"rApertureSize":0.45982,"rApertureShape":0,"rApertureRotate":0,"sLightPathDepth":"64","cMis":false,"sTonemap":"None","fAppearance1":null,"rMetallic1":1,"rTint1":1,"rVEmission1":0,"rScatterAniso1":0,"fAppearance2":null,"rBrightness2":0.875,"rContrast2":0.2,"rMetallic2":1,"rIor2":1,"rTint2":1,"rVEmission2":0,"rScatterAniso2":0,"rVSharpSca":0}}
    },
    {
        "name": "Red Roots",
        "id": "red-roots",
        "state": {"input":"f0(p)=vec2(p.x^2-p.y^2+z,2p.xp.y)\nf(p)=f0(p)+vec2(x,y)\nc=f0(f(f(vec2(x,y))))\nlog(dot(c,c))=0","params":{"cLatex":true,"cAutoUpdate":true,"fBasic":false,"sStep":"0.01","bLight":false,"bYup":false,"sSpp":"256","sLightPathDepth":"64","cMis":false,"sClip":"1","cClosed":true,"sDenoise":"null","sTonemap":"None","rScale1":0.5,"rScale2":0.5,"fAppearance1":true,"sColor":"2","sField":"0","bGrid":false,"bDiscontinuity":true,"rOpacity":0,"rIor":1.7009999999999998,"rRoughness1":0,"rMetallic1":1,"rTint1":1,"rEmission1":0,"rAbsorb1":0.49995,"rVEmission1":0,"rScatter1":0,"rScatterAniso1":0,"fAppearance2":true,"rBrightness2":0.875,"rContrast2":0.2,"rRoughness2":0.57,"rMetallic2":1,"rIor2":0.9990000000000001,"rTint2":1,"rEmission2":0,"rAbsorb2":0,"rVDecayAbs":0.5496800000000001,"rVAbsorbHue":0.55,"rVAbsorbChr":0.4,"rVEmission2":0,"rScatter2":0.79398,"rScatterAniso2":0,"rVDecaySca":0.19818000000000002,"rVSharpSca":0,"fLighting":false,"rTheta":2.7206192380087613,"rPhi":2.491282974296706,"rLightIntensity":0.5,"rLightSky":0,"rLightAmbient":0,"rLightSoftness":0.836164,"rLightHardness":1,"fCamera":true,"fov":1.1442308762904747,"rExposure":0.5,"ry":0,"rCameraDistortion":0.5,"rFocalLength":0.5,"rApertureSize":0.28538,"rApertureShape":0,"rApertureRotate":0},"state":{"name":"spirulae.implicit3-rt.state","iFrame":256,"width":924,"height":595,"screenCenter":{"x":0.32812050823525435,"y":0.5},"defaultScreenCenter":true,"worldSpace":true,"iTime":-1,"rz":-3.014513020910303,"rx":-1.6279644737231007,"ry":0,"fov":1.1442308762904747,"scale":0.4398274428524963,"clipSize":[2,2,2],"rTheta":2.7206192380087613,"rPhi":2.491282974296706,"renderNeeded":false,"cLatex":true,"cAutoUpdate":true,"fBasic":false,"sStep":"0.01","bLight":false,"bYup":false,"sSpp":256,"sClip":"1","cClosed":true,"sDenoise":"null","rScale1":0.5,"rScale2":0.5,"sColor":"2","sField":"0","bGrid":false,"bDiscontinuity":true,"fAppearance":false,"rOpacity":0,"rIor":1.7009999999999998,"rRoughness1":0,"rRoughness2":0.57,"rEmission1":0,"rEmission2":0,"rAbsorb1":0.49995,"rAbsorb2":0,"rScatter1":0,"rScatter2":0.79398,"rVDecayAbs":0.5496800000000001,"rVDecaySca":0.19818000000000002,"rVAbsorbHue":0.55,"rVAbsorbChr":0.4,"rVAbsorbBri":0.97,"fLighting":false,"rLightIntensity":0.5,"rLightSky":0,"rLightAmbient":0,"rLightSoftness":0.836164,"rLightHardness":1,"fCamera":true,"rExposure":0.5,"rCameraDistortion":0.5,"rFocalLength":0.5,"rApertureSize":0.28538,"rApertureShape":0,"rApertureRotate":0,"sLightPathDepth":"64","cMis":false,"sTonemap":"None","fAppearance1":null,"rMetallic1":1,"rTint1":1,"rVEmission1":0,"rScatterAniso1":0,"fAppearance2":null,"rBrightness2":0.875,"rContrast2":0.2,"rMetallic2":1,"rIor2":1,"rTint2":1,"rVEmission2":0,"rScatterAniso2":0,"rVSharpSca":0}}
    },
    {
        "name": "Spiral Sky",
        "id": "spiral-sky",
        "state": {"input":"# polar repetition\nr=hypot(x,y)\na0=atan(y,x)\na=asin(sin(1.5a0))/1.5\nw=0.1+0.5ln(ln(e^-z+e))\n# cross section of a single vine\nx1=rcos(a)-w^2\ny1=rsin(a)\nw_t=w*(1+0.1sin(3(atan(y1,x1)))\nd(x,y,z)=hypot(x1,y1)-(0.5w_t^1.3-0.1)\n# twist the strands\nr_z=5asinh(z-2)\n5d(xcos(r_z)-ysin(r_z),xsin(r_z)+ycos(r_z),z-3)","params":{"cLatex":false,"cAutoUpdate":true,"fBasic":false,"sStep":"0.004","bLight":false,"bYup":false,"sSpp":"256","sLightPathDepth":"64","cMis":false,"sClip":"1","cClosed":true,"sDenoise":"null","sTonemap":"None","rScale1":0.61858,"rScale2":0.5,"fAppearance1":true,"sColor":"2","sField":"0","bGrid":false,"bDiscontinuity":true,"rOpacity":0,"rIor":1.323,"rRoughness1":0,"rMetallic1":1,"rTint1":1,"rEmission1":0,"rAbsorb1":0.76626,"rVEmission1":0,"rScatter1":0,"rScatterAniso1":0,"fAppearance2":true,"rBrightness2":0.875,"rContrast2":0.2,"rRoughness2":0.727,"rMetallic2":1,"rIor2":0.9990000000000001,"rTint2":1,"rEmission2":0,"rAbsorb2":0.07227,"rVDecayAbs":0.3156,"rVAbsorbHue":0.55,"rVAbsorbChr":0.4,"rVEmission2":0,"rScatter2":0,"rScatterAniso2":0,"rVDecaySca":0.49985,"rVSharpSca":0,"fLighting":true,"rTheta":1.8849555921538763,"rPhi":2.6295130510546567,"rLightIntensity":0.5,"rLightSky":1,"rLightAmbient":0,"rLightSoftness":0.8002,"rLightHardness":0,"fCamera":true,"fov":1.1805791032925084,"rExposure":0.5,"ry":0,"rCameraDistortion":0.5,"rFocalLength":0.5,"rApertureSize":0.01,"rApertureShape":0,"rApertureRotate":0},"state":{"name":"spirulae.implicit3-rt.state","iFrame":198,"width":924,"height":595,"screenCenter":{"x":0.32812050823525435,"y":0.5},"defaultScreenCenter":true,"worldSpace":true,"iTime":-1,"rz":-2.294513020910304,"rx":-3.1479644737231083,"ry":0,"fov":1.1805791032925084,"scale":46.95676081816304,"clipSize":[2,2,2],"rTheta":1.8849555921538763,"rPhi":2.6295130510546567,"renderNeeded":false,"cLatex":false,"cAutoUpdate":true,"fBasic":false,"sStep":"0.004","bLight":false,"bYup":false,"sSpp":256,"sClip":"1","cClosed":true,"sDenoise":"null","rScale1":0.61858,"rScale2":0.5,"sColor":"2","sField":"0","bGrid":false,"bDiscontinuity":true,"fAppearance":false,"rOpacity":0,"rIor":1.323,"rRoughness1":0,"rRoughness2":0.727,"rEmission1":0,"rEmission2":0,"rAbsorb1":0.76626,"rAbsorb2":0.07227,"rScatter1":0,"rScatter2":0,"rVDecayAbs":0.3156,"rVDecaySca":0.49985,"rVAbsorbHue":0.55,"rVAbsorbChr":0.4,"rVAbsorbBri":0.97,"fLighting":true,"rLightIntensity":0.5,"rLightAmbient":0,"rLightSoftness":0.8002,"rLightHardness":0,"fCamera":true,"rExposure":0.5,"rCameraDistortion":0.5,"rFocalLength":0.5,"rApertureSize":0.01,"rApertureShape":0,"rApertureRotate":0,"rLightSky":1,"sLightPathDepth":"64","cMis":false,"sTonemap":"None","fAppearance1":null,"rMetallic1":1,"rTint1":1,"rVEmission1":0,"rScatterAniso1":0,"fAppearance2":null,"rBrightness2":0.875,"rContrast2":0.2,"rMetallic2":1,"rIor2":1,"rTint2":1,"rVEmission2":0,"rScatterAniso2":0,"rVSharpSca":0}}
    },
    {
        "name": "Goursat Surface",
        "id": "goursat-surface",
        "state": {"input":"2(x^4+y^4+z^4)-3(x^2+y^2+z^2)+2","params":{"cLatex":true,"cAutoUpdate":true,"fBasic":false,"sStep":"0.01","bLight":true,"bYup":false,"sSpp":"256","sLightPathDepth":"64","cMis":false,"sClip":"1","cClosed":true,"sDenoise":"null","sTonemap":"None","rScale1":0.60094,"rScale2":0.5,"fAppearance1":true,"sColor":"1","sField":"0","bGrid":false,"bDiscontinuity":true,"rOpacity":0,"rIor":1.7009999999999998,"rRoughness1":0,"rMetallic1":1,"rTint1":1,"rEmission1":0,"rAbsorb1":0.30888,"rVEmission1":0,"rScatter1":0,"rScatterAniso1":0,"fAppearance2":true,"rBrightness2":0.875,"rContrast2":0.2,"rRoughness2":0.3,"rMetallic2":1,"rIor2":0.9990000000000001,"rTint2":1,"rEmission2":0,"rAbsorb2":0,"rVDecayAbs":0.5004000000000001,"rVAbsorbHue":0.55,"rVAbsorbChr":0.4,"rVEmission2":0,"rScatter2":0,"rScatterAniso2":0,"rVDecaySca":0.15162,"rVSharpSca":0,"fLighting":true,"rTheta":-1.3194689145077132,"rPhi":1.7058848108992577,"rLightIntensity":0.5,"rLightSky":0,"rLightAmbient":0,"rLightSoftness":0.8002,"rLightHardness":0,"fCamera":true,"fov":0.7863406411935253,"rExposure":0.5,"ry":0,"rCameraDistortion":0.5,"rFocalLength":0.5,"rApertureSize":0.37946,"rApertureShape":0,"rApertureRotate":0},"state":{"name":"spirulae.implicit3-rt.state","iFrame":94,"width":924,"height":595,"screenCenter":{"x":0.32812050823525435,"y":0.5},"defaultScreenCenter":true,"worldSpace":true,"iTime":-1,"rz":-4.314513020910295,"rx":-1.7079644737231008,"ry":0,"fov":0.7863406411935253,"scale":0.859871083114945,"clipSize":[2,2,2],"rTheta":-1.3194689145077132,"rPhi":1.7058848108992577,"renderNeeded":false,"cLatex":true,"cAutoUpdate":true,"fBasic":false,"sStep":"0.01","bLight":true,"bYup":false,"sSpp":256,"sClip":"1","cClosed":true,"sDenoise":"null","rScale1":0.60094,"rScale2":0.5,"sColor":"1","sField":"0","bGrid":false,"bDiscontinuity":true,"fAppearance":false,"rOpacity":0,"rIor":1.7009999999999998,"rRoughness1":0,"rRoughness2":0.3,"rEmission1":0,"rEmission2":0,"rAbsorb1":0.30888,"rAbsorb2":0,"rScatter1":0,"rScatter2":0,"rVDecayAbs":0.5004000000000001,"rVDecaySca":0.15162,"rVAbsorbHue":0.55,"rVAbsorbChr":0.4,"rVAbsorbBri":0.97,"fLighting":true,"rLightIntensity":0.5,"rLightAmbient":0,"rLightSoftness":0.8002,"rLightHardness":0,"fCamera":true,"rExposure":0.5,"rCameraDistortion":0.5,"rFocalLength":0.5,"rApertureSize":0.37946,"rApertureShape":0,"rApertureRotate":0,"rLightSky":0,"sLightPathDepth":"64","cMis":false,"sTonemap":"None","fAppearance1":null,"rMetallic1":1,"rTint1":1,"rVEmission1":0,"rScatterAniso1":0,"fAppearance2":null,"rBrightness2":0.875,"rContrast2":0.2,"rMetallic2":1,"rIor2":1,"rTint2":1,"rVEmission2":0,"rScatterAniso2":0,"rVSharpSca":0}}
    },
    {
        "name": "Soft Barth",
        "id": "soft-barth",
        "state": {"input":"3(x^2+y^2+z^2-1)^2-4(x^2-y^2)(y^2-z^2)(z^2-x^2)","params":{"cLatex":true,"cAutoUpdate":true,"fBasic":false,"sStep":"0.01","bLight":true,"bYup":false,"sSpp":"256","sLightPathDepth":"64","cMis":false,"sClip":"1","cClosed":true,"sDenoise":"null","sTonemap":"None","rScale1":0.60094,"rScale2":0.5,"fAppearance1":true,"sColor":"1","sField":"0","bGrid":false,"bDiscontinuity":true,"rOpacity":1,"rIor":1.7009999999999998,"rRoughness1":0.418,"rMetallic1":1,"rTint1":1,"rEmission1":0,"rAbsorb1":0.49995,"rVEmission1":0,"rScatter1":0,"rScatterAniso1":0,"fAppearance2":true,"rBrightness2":0.875,"rContrast2":0.2,"rRoughness2":0.3,"rMetallic2":1,"rIor2":0.9990000000000001,"rTint2":1,"rEmission2":0,"rAbsorb2":0,"rVDecayAbs":0.5004000000000001,"rVAbsorbHue":0.55,"rVAbsorbChr":0.4,"rVEmission2":0,"rScatter2":0.87318,"rScatterAniso2":0,"rVDecaySca":0.15162,"rVSharpSca":0,"fLighting":true,"rTheta":3.5060174014062095,"rPhi":2.356194490192345,"rLightIntensity":0.5,"rLightSky":0.2,"rLightAmbient":0,"rLightSoftness":0.8002,"rLightHardness":0,"fCamera":true,"fov":0.7863406411935253,"rExposure":0.5,"ry":0,"rCameraDistortion":0.651,"rFocalLength":0.5,"rApertureSize":0.745,"rApertureShape":0,"rApertureRotate":0},"state":{"name":"spirulae.implicit3-rt.state","iFrame":96,"width":924,"height":595,"screenCenter":{"x":0.32812050823525435,"y":0.5},"defaultScreenCenter":true,"worldSpace":true,"iTime":-1,"rz":-2.244513020910289,"rx":-1.5579644737231007,"ry":0,"fov":0.7863406411935253,"scale":0.4121479861975537,"clipSize":[2,2,2],"rTheta":3.5060174014062095,"rPhi":2.356194490192345,"renderNeeded":false,"cLatex":true,"cAutoUpdate":true,"fBasic":false,"sStep":"0.01","bLight":true,"bYup":false,"sSpp":256,"sClip":"1","cClosed":true,"sDenoise":"null","rScale1":0.60094,"rScale2":0.5,"sColor":"1","sField":"0","bGrid":false,"bDiscontinuity":true,"fAppearance":false,"rOpacity":1,"rIor":1.7009999999999998,"rRoughness1":0.418,"rRoughness2":0.3,"rEmission1":0,"rEmission2":0,"rAbsorb1":0.49995,"rAbsorb2":0,"rScatter1":0,"rScatter2":0.87318,"rVDecayAbs":0.5004000000000001,"rVDecaySca":0.15162,"rVAbsorbHue":0.55,"rVAbsorbChr":0.4,"rVAbsorbBri":0.97,"fLighting":true,"rLightIntensity":0.5,"rLightAmbient":0,"rLightSoftness":0.8002,"rLightHardness":0,"fCamera":true,"rExposure":0.5,"rCameraDistortion":0.651,"rFocalLength":0.5,"rApertureSize":0.745,"rApertureShape":0,"rApertureRotate":0,"rLightSky":0.2,"sLightPathDepth":"64","cMis":false,"sTonemap":"None","fAppearance1":null,"rMetallic1":1,"rTint1":1,"rVEmission1":0,"rScatterAniso1":0,"fAppearance2":null,"rBrightness2":0.875,"rContrast2":0.2,"rMetallic2":1,"rIor2":1,"rTint2":1,"rVEmission2":0,"rScatterAniso2":0,"rVSharpSca":0}}
    },
    {
        "name": "Genus 3",
        "id": "genus-3",
        "state": {"input":"4((x^2-1)^2+(y^2-1)^2+(z^2-1)^2+4(x^2y^2+x^2z^2+y^2z^2)+8xyz-2(x^2+y^2+z^2))","params":{"cLatex":true,"cAutoUpdate":true,"fBasic":false,"sStep":"0.01","bLight":true,"bYup":false,"sSpp":"256","sLightPathDepth":"64","cMis":false,"sClip":"1","cClosed":true,"sDenoise":"null","sTonemap":"None","rScale1":0.60094,"rScale2":0.5,"fAppearance1":false,"sColor":"2","sField":"0","bGrid":false,"bDiscontinuity":true,"rOpacity":1,"rIor":3,"rRoughness1":0.095,"rMetallic1":0,"rTint1":0,"rEmission1":0,"rAbsorb1":0.49995,"rVEmission1":0,"rScatter1":0,"rScatterAniso1":0,"fAppearance2":true,"rBrightness2":0.875,"rContrast2":0.2,"rRoughness2":0.3,"rMetallic2":1,"rIor2":0.9990000000000001,"rTint2":1,"rEmission2":0,"rAbsorb2":0,"rVDecayAbs":0.44848,"rVAbsorbHue":0.55,"rVAbsorbChr":0.4,"rVEmission2":0,"rScatter2":0,"rScatterAniso2":0,"rVDecaySca":0.6957899999999999,"rVSharpSca":0,"fLighting":true,"rTheta":0.22619467105846502,"rPhi":1.9823449644151594,"rLightIntensity":0.569,"rLightSky":0.2,"rLightAmbient":0,"rLightSoftness":0.783217,"rLightHardness":0,"fCamera":true,"fov":0.7863406411935253,"rExposure":0.5,"ry":0,"rCameraDistortion":0.5,"rFocalLength":0.5,"rApertureSize":0.2991,"rApertureShape":0,"rApertureRotate":0},"state":{"name":"spirulae.implicit3-rt.state","iFrame":225,"width":924,"height":595,"screenCenter":{"x":0.32812050823525435,"y":0.5},"defaultScreenCenter":true,"worldSpace":true,"iTime":-1,"rz":-6.354513020910291,"rx":-1.5079644737231006,"ry":0,"fov":0.7863406411935253,"scale":0.7609612788657675,"clipSize":[2,2,2],"rTheta":0.22619467105846502,"rPhi":1.9823449644151594,"renderNeeded":false,"cLatex":true,"cAutoUpdate":true,"fBasic":false,"sStep":"0.01","bLight":true,"bYup":false,"sSpp":256,"sClip":"1","cClosed":true,"sDenoise":"null","rScale1":0.60094,"rScale2":0.5,"sColor":"2","sField":"0","bGrid":false,"bDiscontinuity":true,"fAppearance":false,"rOpacity":1,"rIor":3,"rRoughness1":0.095,"rRoughness2":0.3,"rEmission1":0,"rEmission2":0,"rAbsorb1":0.49995,"rAbsorb2":0,"rScatter1":0,"rScatter2":0,"rVDecayAbs":0.44848,"rVDecaySca":0.6957899999999999,"rVAbsorbHue":0.55,"rVAbsorbChr":0.4,"rVAbsorbBri":0.97,"fLighting":true,"rLightIntensity":0.569,"rLightAmbient":0,"rLightSoftness":0.783217,"rLightHardness":0,"fCamera":true,"rExposure":0.5,"rCameraDistortion":0.5,"rFocalLength":0.5,"rApertureSize":0.2991,"rApertureShape":0,"rApertureRotate":0,"rLightSky":0.2,"sLightPathDepth":"64","cMis":false,"sTonemap":"None","fAppearance1":false,"rMetallic1":0,"rTint1":0,"rVEmission1":0,"rScatterAniso1":0,"fAppearance2":true,"rBrightness2":0.875,"rContrast2":0.2,"rMetallic2":1,"rIor2":0.9990000000000001,"rTint2":1,"rVEmission2":0,"rScatterAniso2":0,"rVSharpSca":0}}
    },
    {
        "name": "Shiny Tower",
        "id": "shiny-tower",
        "state": {"input":"4z+6=1/((sin(4x)sin(4y))^2+0.4sqrt(x^2+y^2+0.005z^2))-4sin(8z)","params":{"cLatex":true,"cAutoUpdate":true,"fBasic":false,"sStep":"0.01","bLight":true,"bYup":false,"sSpp":"256","sLightPathDepth":"64","cMis":false,"sClip":"1","cClosed":true,"sDenoise":"null","sTonemap":"None","rScale1":0.5,"rScale2":0.5,"fAppearance1":true,"sColor":"2","sField":"0","bGrid":true,"bDiscontinuity":true,"rOpacity":1,"rIor":1.7009999999999998,"rRoughness1":0,"rMetallic1":1,"rTint1":1,"rEmission1":0,"rAbsorb1":0.49995,"rVEmission1":0,"rScatter1":0,"rScatterAniso1":0,"fAppearance2":true,"rBrightness2":0.875,"rContrast2":0.2,"rRoughness2":0.262,"rMetallic2":1,"rIor2":0.9990000000000001,"rTint2":1,"rEmission2":0,"rAbsorb2":0,"rVDecayAbs":0.5004000000000001,"rVAbsorbHue":0,"rVAbsorbChr":0.4,"rVEmission2":0,"rScatter2":0,"rScatterAniso2":0,"rVDecaySca":0.01,"rVSharpSca":0,"fLighting":true,"rTheta":3.1667253948185117,"rPhi":2.767743127812608,"rLightIntensity":0.5,"rLightSky":0,"rLightAmbient":0,"rLightSoftness":0.8002,"rLightHardness":0,"fCamera":true,"fov":0.7863406411935253,"rExposure":0.5,"ry":0,"rCameraDistortion":0.5,"rFocalLength":0.5,"rApertureSize":0.01,"rApertureShape":0,"rApertureRotate":0},"state":{"name":"spirulae.implicit3-rt.state","iFrame":256,"width":924,"height":600,"screenCenter":{"x":0.32812050823525435,"y":0.5},"defaultScreenCenter":true,"worldSpace":true,"iTime":-1,"rz":-8.934513020910273,"rx":-1.4479644737231003,"ry":0,"fov":0.7863406411935253,"scale":0.3731507429717974,"clipSize":[2,2,2],"rTheta":3.1667253948185117,"rPhi":2.767743127812608,"renderNeeded":false,"cLatex":true,"cAutoUpdate":true,"fBasic":false,"sStep":"0.01","bLight":true,"bYup":false,"sSpp":256,"sClip":"1","cClosed":true,"sDenoise":"null","rScale1":0.5,"rScale2":0.5,"sColor":"2","sField":"0","bGrid":true,"bDiscontinuity":true,"fAppearance":false,"rOpacity":1,"rIor":1.7009999999999998,"rRoughness1":0,"rRoughness2":0.262,"rEmission1":0,"rEmission2":0,"rAbsorb1":0.49995,"rAbsorb2":0,"rScatter1":0,"rScatter2":0,"rVDecayAbs":0.5004000000000001,"rVDecaySca":0.01,"rVAbsorbHue":0,"rVAbsorbChr":0.4,"rVAbsorbBri":0.97,"fLighting":true,"rLightIntensity":0.5,"rLightSky":0,"rLightAmbient":0,"rLightSoftness":0.8002,"rLightHardness":0,"fCamera":true,"rExposure":0.5,"rCameraDistortion":0.5,"rFocalLength":0.5,"rApertureSize":0.01,"rApertureShape":0,"rApertureRotate":0,"sLightPathDepth":"64","cMis":false,"sTonemap":"None","fAppearance1":true,"rMetallic1":1,"rTint1":1,"rVEmission1":0,"rScatterAniso1":0,"fAppearance2":true,"rBrightness2":0.875,"rContrast2":0.2,"rMetallic2":1,"rIor2":0.9990000000000001,"rTint2":1,"rVEmission2":0,"rScatterAniso2":0,"rVSharpSca":0}}
    },
    {
        "name": "MandelTorus",
        "id": "mandeltorus",
        "state": {"input":"# parameters\nk=-1\nm=6\nn=6\nR=2\n# cartesian to toric\nr=hypot(hypot(x,y)-R,z)\na=atan(y,x)\nb=atan(hypot(x,y)-R,z)\n# toric to cartisian\nu(x,y,z)=cos(ma)(R+r^ksin(nb))\nv(x,y,z)=sin(ma)(R+r^ksin(nb))\nw(x,y,z)=r^kcos(nb)\n# iterations\nu1(x,y,z)=u(u(x,y,z)+x,v(x,y,z)+y,w(x,y,z)+z)\nv1(x,y,z)=v(u(x,y,z)+x,v(x,y,z)+y,w(x,y,z)+z)\nw1(x,y,z)=w(u(x,y,z)+x,v(x,y,z)+y,w(x,y,z)+z)\nu2(x,y,z)=u(u1(x,y,z)+x,v1(x,y,z)+y,w1(x,y,z)+z)\nv2(x,y,z)=v(u1(x,y,z)+x,v1(x,y,z)+y,w1(x,y,z)+z)\nw2(x,y,z)=w(u1(x,y,z)+x,v1(x,y,z)+y,w1(x,y,z)+z)\n# post processing\nT(x,y,z)=log(hypot(u2(x,y,z),v2(x,y,z),w2(x,y,z)))\nT(0.6x(R+1),0.6z(R+1),0.6y(R+1))=R*(e^-k+k-1)/(1+e^-k)","params":{"cLatex":false,"cAutoUpdate":true,"fBasic":false,"sStep":"0.01","bLight":true,"bYup":false,"sSpp":"256","sLightPathDepth":"64","cMis":false,"sClip":"2","cClosed":true,"sDenoise":"null","sTonemap":"None","rScale1":0.5578200000000001,"rScale2":0.99,"fAppearance1":true,"sColor":"2","sField":"0","bGrid":false,"bDiscontinuity":true,"rOpacity":0,"rIor":1.7009999999999998,"rRoughness1":0,"rMetallic1":1,"rTint1":1,"rEmission1":0,"rAbsorb1":0.77814,"rVEmission1":0,"rScatter1":0,"rScatterAniso1":0,"fAppearance2":false,"rBrightness2":0.875,"rContrast2":0.2,"rRoughness2":1,"rMetallic2":1,"rIor2":0.9990000000000001,"rTint2":1,"rEmission2":0,"rAbsorb2":0,"rVDecayAbs":0.5004000000000001,"rVAbsorbHue":0,"rVAbsorbChr":0.4,"rVEmission2":0,"rScatter2":0.99,"rScatterAniso2":0,"rVDecaySca":0.08080999999999999,"rVSharpSca":1,"fLighting":true,"rTheta":-0.4209734155810323,"rPhi":2.258805117931061,"rLightIntensity":0.523,"rLightSky":0,"rLightAmbient":0,"rLightSoftness":0.701299,"rLightHardness":0,"fCamera":true,"fov":0.7863406411935253,"rExposure":0.5,"ry":0,"rCameraDistortion":0.5,"rFocalLength":0.5,"rApertureSize":0.01,"rApertureShape":0,"rApertureRotate":0},"state":{"name":"spirulae.implicit3-rt.state","iFrame":143,"width":924,"height":595,"screenCenter":{"x":0.32812050823525435,"y":0.5},"defaultScreenCenter":true,"worldSpace":true,"iTime":-1,"rz":-9.384513020910276,"rx":-1.7979644737231006,"ry":0,"fov":0.7863406411935253,"scale":0.4755074143727509,"clipSize":[2,2,2],"rTheta":-0.4209734155810323,"rPhi":2.258805117931061,"renderNeeded":false,"cLatex":false,"cAutoUpdate":true,"fBasic":false,"sStep":"0.01","bLight":true,"bYup":false,"sSpp":256,"sClip":"2","cClosed":true,"sDenoise":"null","rScale1":0.5578200000000001,"rScale2":0.99,"sColor":"2","sField":"0","bGrid":false,"bDiscontinuity":true,"fAppearance":false,"rOpacity":0,"rIor":1.7009999999999998,"rRoughness1":0,"rRoughness2":1,"rEmission1":0,"rEmission2":0,"rAbsorb1":0.77814,"rAbsorb2":0,"rScatter1":0,"rScatter2":0.99,"rVDecayAbs":0.5004000000000001,"rVDecaySca":0.08080999999999999,"rVAbsorbHue":0,"rVAbsorbChr":0.4,"rVAbsorbBri":0.97,"fLighting":true,"rLightIntensity":0.523,"rLightSky":0,"rLightAmbient":0,"rLightSoftness":0.701299,"rLightHardness":0,"fCamera":true,"rExposure":0.5,"rCameraDistortion":0.5,"rFocalLength":0.5,"rApertureSize":0.01,"rApertureShape":0,"rApertureRotate":0,"sLightPathDepth":"64","cMis":false,"sTonemap":"None","fAppearance1":true,"rMetallic1":1,"rTint1":1,"rVEmission1":0,"rScatterAniso1":0,"fAppearance2":false,"rBrightness2":0.875,"rContrast2":0.2,"rMetallic2":1,"rIor2":0.9990000000000001,"rTint2":1,"rVEmission2":0,"rScatterAniso2":0,"rVSharpSca":1}}
    },
    {
        "name": "Coronavirus",
        "id": "coronavirus",
        "state": {"input":"0=z^6-5(x^2+y^2)z^4+5(x^2+y^2)^2z^2+2(5x^4-10x^2y^2+y^4)yz-1.002(x^2+y^2+z^2)^3+0.2","params":{"cLatex":true,"cAutoUpdate":true,"fBasic":false,"sStep":"0.01","bLight":false,"bYup":false,"sSpp":"256","sLightPathDepth":"64","cMis":false,"sClip":"1","cClosed":true,"sDenoise":"null","sTonemap":"None","rScale1":0.5,"rScale2":0.5,"fAppearance1":true,"sColor":"2","sField":"0","bGrid":false,"bDiscontinuity":true,"rOpacity":0,"rIor":1.7009999999999998,"rRoughness1":0,"rMetallic1":1,"rTint1":1,"rEmission1":0.402,"rAbsorb1":0.49995,"rVEmission1":0,"rScatter1":0,"rScatterAniso1":0,"fAppearance2":true,"rBrightness2":0.875,"rContrast2":0.2,"rRoughness2":0.3,"rMetallic2":1,"rIor2":0.9990000000000001,"rTint2":1,"rEmission2":0,"rAbsorb2":0.78012,"rVDecayAbs":0.5004000000000001,"rVAbsorbHue":0,"rVAbsorbChr":0.4,"rVEmission2":0,"rScatter2":0.13365000000000002,"rScatterAniso2":0,"rVDecaySca":0.49694,"rVSharpSca":0,"fLighting":true,"rTheta":3.5060174014062095,"rPhi":3.141592653589793,"rLightIntensity":0.5,"rLightSky":1,"rLightAmbient":0,"rLightSoftness":0.8002,"rLightHardness":0,"fCamera":true,"fov":0.7863406411935253,"rExposure":0.5,"ry":0,"rCameraDistortion":0.346,"rFocalLength":0.5,"rApertureSize":0.01,"rApertureShape":0,"rApertureRotate":0},"state":{"name":"spirulae.implicit3-rt.state","iFrame":104,"width":924,"height":595,"screenCenter":{"x":0.32812050823525435,"y":0.5},"defaultScreenCenter":true,"worldSpace":true,"iTime":-1,"rz":-2.884513020910305,"rx":-1.6479644737231007,"ry":0,"fov":0.7863406411935253,"scale":0.4560408891378082,"clipSize":[2,2,2],"rTheta":3.5060174014062095,"rPhi":3.141592653589793,"renderNeeded":false,"cLatex":true,"cAutoUpdate":true,"fBasic":false,"sStep":"0.01","bLight":false,"bYup":false,"sSpp":256,"sClip":"1","cClosed":true,"sDenoise":"null","rScale1":0.5,"rScale2":0.5,"sColor":"2","sField":"0","bGrid":false,"bDiscontinuity":true,"fAppearance":false,"rOpacity":0,"rIor":1.7009999999999998,"rRoughness1":0,"rRoughness2":0.3,"rEmission1":0.402,"rEmission2":0,"rAbsorb1":0.49995,"rAbsorb2":0.78012,"rScatter1":0,"rScatter2":0.13365000000000002,"rVDecayAbs":0.5004000000000001,"rVDecaySca":0.49694,"rVAbsorbHue":0,"rVAbsorbChr":0.4,"rVAbsorbBri":0.97,"fLighting":true,"rLightIntensity":0.5,"rLightAmbient":0,"rLightSoftness":0.8002,"rLightHardness":0,"fCamera":true,"rExposure":0.5,"rCameraDistortion":0.346,"rFocalLength":0.5,"rApertureSize":0.01,"rApertureShape":0,"rApertureRotate":0,"rLightSky":1,"sLightPathDepth":"64","cMis":false,"sTonemap":"None","fAppearance1":null,"rMetallic1":1,"rTint1":1,"rVEmission1":0,"rScatterAniso1":0,"fAppearance2":null,"rBrightness2":0.875,"rContrast2":0.2,"rMetallic2":1,"rIor2":1,"rTint2":1,"rVEmission2":0,"rScatterAniso2":0,"rVSharpSca":0}}
    },
    {
        "name": "Misty DNA",
        "id": "misty-dna",
        "state": {"input":"# polar coordinates\nr=hypot(x,y)\na=atan(y,x)+0.8z\n# strand\ns0=hypot(sin(a),sin(pi-a),r-1)-0.25\ns=s0-0.15sin(10x)sin(10y)sin(10z)\n# cross line\nc=hypot(sin(3z),rsin(a))-0.2\n# double helix\ns_max(a,b,k)=1/k*ln(e^(ka)+e^(kb))\nh(x,y,z)=s_max(-s,-s_max(c,r-1,10),10)+0.05\n# repetition\nh_r=h(mod(x-sin(0.4y),8)-4,mod(y,6)-3,z+0.1sin(x)sin(y))\n# bubbles\nb=(1.01+sin(0.9x+1.2y))(sin(2x)sin(2y)sin(2z-sin(2x)sin(y))-e^(-0.05sin(x)cos(y)))\n# put together\ng(x,y,z)=max(h_r,b)\n-0.5g(2x,2y,2z)=0","params":{"cLatex":false,"cAutoUpdate":true,"fBasic":false,"sStep":"0.01","bLight":true,"bYup":false,"sSpp":"256","sLightPathDepth":"64","cMis":false,"sClip":"1","cClosed":true,"sDenoise":"null","sTonemap":"None","rScale1":0.76852,"rScale2":0.5,"fAppearance1":true,"sColor":"2","sField":"0","bGrid":false,"bDiscontinuity":true,"rOpacity":1,"rIor":1.7009999999999998,"rRoughness1":0,"rMetallic1":1,"rTint1":1,"rEmission1":0,"rAbsorb1":0.49995,"rVEmission1":0,"rScatter1":0,"rScatterAniso1":0,"fAppearance2":true,"rBrightness2":0.875,"rContrast2":0.2,"rRoughness2":1,"rMetallic2":1,"rIor2":0.9990000000000001,"rTint2":1,"rEmission2":0,"rAbsorb2":0,"rVDecayAbs":0.5496800000000001,"rVAbsorbHue":0.55,"rVAbsorbChr":0.4,"rVEmission2":0,"rScatter2":0.79398,"rScatterAniso2":0,"rVDecaySca":0.19818000000000002,"rVSharpSca":0,"fLighting":true,"rTheta":2.657787384936965,"rPhi":2.6734953482049137,"rLightIntensity":0.42,"rLightSky":1,"rLightAmbient":0,"rLightSoftness":0.6753250000000001,"rLightHardness":0,"fCamera":true,"fov":1.79570294486539,"rExposure":0.5,"ry":0,"rCameraDistortion":0.5,"rFocalLength":0.5,"rApertureSize":0.72834,"rApertureShape":0,"rApertureRotate":0},"state":{"name":"spirulae.implicit3-rt.state","iFrame":75,"width":924,"height":595,"screenCenter":{"x":0.32812050823525435,"y":0.5},"defaultScreenCenter":true,"worldSpace":true,"iTime":-1,"rz":-7.534513020910266,"rx":-2.8779644737230865,"ry":0,"fov":1.79570294486539,"scale":0.7806933808865013,"clipSize":[2,2,2],"rTheta":2.657787384936965,"rPhi":2.6734953482049137,"renderNeeded":false,"cLatex":false,"cAutoUpdate":true,"fBasic":false,"sStep":"0.01","bLight":true,"bYup":false,"sSpp":256,"sClip":"1","cClosed":true,"sDenoise":"null","rScale1":0.76852,"rScale2":0.5,"sColor":"2","sField":"0","bGrid":false,"bDiscontinuity":true,"fAppearance":false,"rOpacity":1,"rIor":1.7009999999999998,"rRoughness1":0,"rRoughness2":1,"rEmission1":0,"rEmission2":0,"rAbsorb1":0.49995,"rAbsorb2":0,"rScatter1":0,"rScatter2":0.79398,"rVDecayAbs":0.5496800000000001,"rVDecaySca":0.19818000000000002,"rVAbsorbHue":0.55,"rVAbsorbChr":0.4,"rVAbsorbBri":0.97,"fLighting":true,"rLightIntensity":0.42,"rLightSky":1,"rLightAmbient":0,"rLightSoftness":0.6753250000000001,"rLightHardness":0,"fCamera":true,"rExposure":0.5,"rCameraDistortion":0.5,"rFocalLength":0.5,"rApertureSize":0.72834,"rApertureShape":0,"rApertureRotate":0,"sLightPathDepth":"64","cMis":false,"sTonemap":"None","fAppearance1":null,"rMetallic1":1,"rTint1":1,"rVEmission1":0,"rScatterAniso1":0,"fAppearance2":null,"rBrightness2":0.875,"rContrast2":0.2,"rMetallic2":1,"rIor2":1,"rTint2":1,"rVEmission2":0,"rScatterAniso2":0,"rVSharpSca":0}}
    },
    {
        "name": "Ghost Swamp",
        "id": "ghost-swamp",
        "state": {"input":"s_max(x,y,k)=1/kln(e^(kx)+e^(ky))\nx1=hypot(x,0.05)\nf=hypot(x,2y,1.1z-0.5cos(z)-0.1)-2\ne1=hypot(x1-0.8,z-0.5)-0.4\nn(x,y)=sqrt(x^2+xy+y^2)-0.15\ns=s_max(f,s_max(-e1,-n(x1-0.1,z+0.3),10),5)\ns-0.2(0.5-0.5tanh(5(z+1.)))cos(10x)\n","params":{"cLatex":false,"cAutoUpdate":true,"fBasic":false,"sStep":"0.01","bLight":false,"bYup":false,"sSpp":"256","sLightPathDepth":"64","cMis":false,"sClip":"1","cClosed":true,"sDenoise":"null","sTonemap":"None","rScale1":0.5,"rScale2":0.5,"fAppearance1":true,"sColor":"1","sField":"0","bGrid":false,"bDiscontinuity":true,"rOpacity":1,"rIor":1.7009999999999998,"rRoughness1":0.274,"rMetallic1":1,"rTint1":1,"rEmission1":0,"rAbsorb1":0.39798,"rVEmission1":0,"rScatter1":0,"rScatterAniso1":0,"fAppearance2":false,"rBrightness2":0.875,"rContrast2":0.2,"rRoughness2":0.3,"rMetallic2":1,"rIor2":0.9990000000000001,"rTint2":1,"rEmission2":0,"rAbsorb2":0.67815,"rVDecayAbs":0.5496800000000001,"rVAbsorbHue":0.142,"rVAbsorbChr":0.4,"rVEmission2":0,"rScatter2":0.94644,"rScatterAniso2":0,"rVDecaySca":0.18072,"rVSharpSca":0.557,"fLighting":true,"rTheta":3.0410616886749198,"rPhi":2.811725424962865,"rLightIntensity":0.5,"rLightSky":0,"rLightAmbient":0,"rLightSoftness":0.754246,"rLightHardness":0,"fCamera":true,"fov":0.7863406411935253,"rExposure":0.5,"ry":0,"rCameraDistortion":0.5,"rFocalLength":0.5,"rApertureSize":0.28538,"rApertureShape":0,"rApertureRotate":0},"state":{"name":"spirulae.implicit3-rt.state","iFrame":256,"width":924,"height":600,"screenCenter":{"x":0.32812050823525435,"y":0.5},"defaultScreenCenter":true,"worldSpace":true,"iTime":-1,"rz":-9.1845130209103,"rx":-1.6079644737231007,"ry":0,"fov":0.7863406411935253,"scale":0.41529224393772285,"clipSize":[2,2,2],"rTheta":3.0410616886749198,"rPhi":2.811725424962865,"renderNeeded":false,"cLatex":false,"cAutoUpdate":true,"fBasic":false,"sStep":"0.01","bLight":false,"bYup":false,"sSpp":"256","sClip":"1","cClosed":true,"sDenoise":"null","rScale1":0.5,"rScale2":0.5,"sColor":"1","sField":"0","bGrid":false,"bDiscontinuity":true,"fAppearance":false,"rOpacity":1,"rIor":1.7009999999999998,"rRoughness1":0.274,"rRoughness2":0.3,"rEmission1":0,"rEmission2":0,"rAbsorb1":0.39798,"rAbsorb2":0.67815,"rScatter1":0,"rScatter2":0.94644,"rVDecayAbs":0.5496800000000001,"rVDecaySca":0.18072,"rVAbsorbHue":0.142,"rVAbsorbChr":0.4,"rVAbsorbBri":0.97,"fLighting":true,"rLightIntensity":0.5,"rLightSky":0,"rLightAmbient":0,"rLightSoftness":0.754246,"rLightHardness":0,"fCamera":true,"rExposure":0.5,"rCameraDistortion":0.5,"rFocalLength":0.5,"rApertureSize":0.28538,"rApertureShape":0,"rApertureRotate":0,"sLightPathDepth":"64","cMis":false,"sTonemap":"None","fAppearance1":true,"rMetallic1":1,"rTint1":1,"rVEmission1":0,"rScatterAniso1":0,"fAppearance2":false,"rBrightness2":0.875,"rContrast2":0.2,"rMetallic2":1,"rIor2":0.9990000000000001,"rTint2":1,"rVEmission2":0,"rScatterAniso2":0,"rVSharpSca":0.557}}
    },
    {
        "name": "Hyperfocus",
        "id": "hyperfocus",
        "state": {"input":"x^2+y^2z+z^2=0.01","params":{"cLatex":true,"cAutoUpdate":true,"fBasic":false,"sStep":"0.01","bLight":false,"bYup":false,"sSpp":"256","sLightPathDepth":"64","cMis":false,"sClip":"1","cClosed":true,"sDenoise":"null","sTonemap":"None","rScale1":0.5,"rScale2":0.5,"fAppearance1":true,"sColor":"2","sField":"0","bGrid":false,"bDiscontinuity":true,"rOpacity":0,"rIor":1.323,"rRoughness1":0,"rMetallic1":1,"rTint1":1,"rEmission1":0,"rAbsorb1":0.50193,"rVEmission1":0,"rScatter1":0,"rScatterAniso1":0,"fAppearance2":true,"rBrightness2":0.875,"rContrast2":0.2,"rRoughness2":0.727,"rMetallic2":1,"rIor2":0.9990000000000001,"rTint2":1,"rEmission2":0,"rAbsorb2":0.07227,"rVDecayAbs":0.3156,"rVAbsorbHue":0.55,"rVAbsorbChr":0.4,"rVEmission2":0,"rScatter2":0,"rScatterAniso2":0,"rVDecaySca":0.49985,"rVSharpSca":0,"fLighting":false,"rTheta":3.141592653589793,"rPhi":2.356194490192345,"rLightIntensity":0.5,"rLightSky":0,"rLightAmbient":0,"rLightSoftness":0.652348,"rLightHardness":1,"fCamera":true,"fov":0.7863406411935253,"rExposure":0.5,"ry":0.3267256359733386,"rCameraDistortion":0.624,"rFocalLength":0.5,"rApertureSize":0.7312799999999999,"rApertureShape":0,"rApertureRotate":0},"state":{"name":"spirulae.implicit3-rt.state","iFrame":256,"width":924,"height":595,"screenCenter":{"x":0.32812050823525435,"y":0.5},"defaultScreenCenter":true,"worldSpace":true,"iTime":-1,"rz":-0.804513020910302,"rx":-1.7079644737231008,"ry":0.3267256359733386,"fov":0.7863406411935253,"scale":0.4295690404714981,"clipSize":[2,2,2],"rTheta":3.141592653589793,"rPhi":2.356194490192345,"renderNeeded":false,"cLatex":true,"cAutoUpdate":true,"fBasic":false,"sStep":"0.01","bLight":false,"bYup":false,"sSpp":256,"sClip":"1","cClosed":true,"sDenoise":"null","rScale1":0.5,"rScale2":0.5,"sColor":"2","sField":"0","bGrid":false,"bDiscontinuity":true,"fAppearance":false,"rOpacity":0,"rIor":1.323,"rRoughness1":0,"rRoughness2":0.727,"rEmission1":0,"rEmission2":0,"rAbsorb1":0.50193,"rAbsorb2":0.07227,"rScatter1":0,"rScatter2":0,"rVDecayAbs":0.3156,"rVDecaySca":0.49985,"rVAbsorbHue":0.55,"rVAbsorbChr":0.4,"rVAbsorbBri":0.97,"fLighting":false,"rLightIntensity":0.5,"rLightAmbient":0,"rLightSoftness":0.652348,"rLightHardness":1,"fCamera":true,"rExposure":0.5,"rCameraDistortion":0.624,"rFocalLength":0.5,"rApertureSize":0.7312799999999999,"rApertureShape":0,"rApertureRotate":0,"rLightSky":0,"sLightPathDepth":"64","cMis":false,"sTonemap":"None","fAppearance1":null,"rMetallic1":1,"rTint1":1,"rVEmission1":0,"rScatterAniso1":0,"fAppearance2":null,"rBrightness2":0.875,"rContrast2":0.2,"rMetallic2":1,"rIor2":1,"rTint2":1,"rVEmission2":0,"rScatterAniso2":0,"rVSharpSca":0}}
    },
    {
        "name": "Foggy Dusk",
        "id": "foggy-dusk",
        "state": {"input":"w(x,y)=sqrt(x^2-|x|y+y^2)\nf0(x,y,z)=min(w(x,y),w(x,z),w(y,z))-1\nr(x)=6/piasin(sin(pi/6x)\nf(n)=f0(r(nx),r(ny),r(nz))\nb=sin(5x)+sin(5y)+sin(5z)\nh=min(f(3),f(9),f(27),f(81),f(243))\nmax(5b,-h)\nc_hsv=vec3(atan2(z,y),0.7,0.8)","params":{"cLatex":false,"cAutoUpdate":true,"fBasic":false,"sStep":"0.01","bLight":true,"bYup":false,"sSpp":"256","sLightPathDepth":"16","cMis":false,"sClip":"1","cClosed":true,"sDenoise":"null","sTonemap":"None","rScale1":0.60094,"rScale2":0.5,"fAppearance1":true,"sColor":"1","sField":"0","bGrid":false,"bDiscontinuity":true,"rOpacity":1,"rIor":1.7009999999999998,"rRoughness1":0.429,"rMetallic1":1,"rTint1":1,"rEmission1":0,"rAbsorb1":0.49995,"rVEmission1":0,"rScatter1":0,"rScatterAniso1":0,"fAppearance2":false,"rBrightness2":0.875,"rContrast2":0.2,"rRoughness2":0.3,"rMetallic2":1,"rIor2":0.9990000000000001,"rTint2":1,"rEmission2":0,"rAbsorb2":0,"rVDecayAbs":0.5004000000000001,"rVAbsorbHue":0.55,"rVAbsorbChr":0.4,"rVEmission2":0,"rScatter2":0.60687,"rScatterAniso2":0,"rVDecaySca":0.4368,"rVSharpSca":0,"fLighting":false,"rTheta":3.5060174014062095,"rPhi":1.5268140296446395,"rLightIntensity":1,"rLightSky":0.82,"rLightAmbient":0,"rLightSoftness":0.757243,"rLightHardness":0,"fCamera":true,"fov":0.7863406411935253,"rExposure":0.5,"ry":0,"rCameraDistortion":0.651,"rFocalLength":0.5,"rApertureSize":0.01,"rApertureShape":0,"rApertureRotate":0},"state":{"name":"spirulae.implicit3-rt.state","iFrame":68,"width":924,"height":600,"screenCenter":{"x":0.5,"y":0.5},"defaultScreenCenter":true,"worldSpace":true,"iTime":-1,"rz":3.7754869790896994,"rx":-1.6879644737230994,"ry":0,"fov":0.7863406411935253,"scale":0.42165290460318333,"clipSize":[2,2,2],"rTheta":3.5060174014062095,"rPhi":1.5268140296446395,"renderNeeded":false,"cLatex":false,"cAutoUpdate":true,"fBasic":false,"sStep":"0.01","bLight":true,"bYup":false,"sSpp":"256","sClip":"1","cClosed":true,"sDenoise":"null","rScale1":0.60094,"rScale2":0.5,"sColor":"1","sField":"0","bGrid":false,"bDiscontinuity":true,"fAppearance":false,"rOpacity":1,"rIor":1.7009999999999998,"rRoughness1":0.429,"rRoughness2":0.3,"rEmission1":0,"rEmission2":0,"rAbsorb1":0.49995,"rAbsorb2":0,"rScatter1":0,"rScatter2":0.60687,"rVDecayAbs":0.5004000000000001,"rVDecaySca":0.4368,"rVAbsorbHue":0.55,"rVAbsorbChr":0.4,"rVAbsorbBri":0.97,"fLighting":false,"rLightIntensity":1,"rLightAmbient":0,"rLightSoftness":0.757243,"rLightHardness":0,"fCamera":true,"rExposure":0.5,"rCameraDistortion":0.651,"rFocalLength":0.5,"rApertureSize":0.01,"rApertureShape":0,"rApertureRotate":0,"rLightSky":0.82,"sTonemap":"None","sLightPathDepth":"16","cMis":false,"fAppearance1":true,"rMetallic1":1,"rTint1":1,"rVEmission1":0,"rScatterAniso1":0,"fAppearance2":false,"rBrightness2":0.875,"rContrast2":0.2,"rMetallic2":1,"rIor2":0.9990000000000001,"rTint2":1,"rVEmission2":0,"rScatterAniso2":0,"rVSharpSca":0}}
    },
    {
        "name": "GrafEq Masked",
        "id": "grafeq-masked",
        "state": {"input":"a=3(z+x+1)\nb=3(z-x+1)\nf(x,y,z)=(3-2z)/9+((2x^2+z^2)/6)^3+100y^2-(sin(min(a*sin(b),b*sin(a)))-cos(max(a*cos(b),b*cos(a)))\nf(x,y,z+0.35)","params":{"cLatex":true,"cAutoUpdate":true,"fBasic":false,"sStep":"0.01","bLight":false,"bYup":false,"sSpp":"256","sSamples":"20","sLightPathDepth":"64","cMis":false,"sClip":"1","cClosed":true,"sDenoise":"null","sTonemap":"None","rScale1":0.549,"rScale2":0.5,"fAppearance1":true,"sColor":"2","sField":"0","bGrid":false,"bDiscontinuity":true,"rOpacity":0,"rIor":1.7009999999999998,"rRoughness1":0,"rMetallic1":1,"rTint1":1,"rEmission1":0,"rAbsorb1":0.80091,"rVEmission1":1,"rScatter1":0,"rScatterAniso1":0,"fAppearance2":true,"rBrightness2":1,"rContrast2":0.149,"rRoughness2":0.227,"rMetallic2":1,"rIor2":0.9990000000000001,"rTint2":1,"rEmission2":0,"rAbsorb2":0.6722100000000001,"rVDecayAbs":0.81016,"rVAbsorbHue":0.803,"rVAbsorbChr":0.4,"rVEmission2":0,"rScatter2":0,"rScatterAniso2":0,"rVDecaySca":0.62304,"rVSharpSca":0,"fLighting":true,"rTheta":3.141592653589793,"rPhi":2.356194490192345,"rLightIntensity":0.5,"rLightSky":0.2,"rLightAmbient":0,"rLightSoftness":0.757243,"rLightHardness":0,"fCamera":true,"fov":0.7863406411935253,"rExposure":0.5,"ry":0,"rCameraDistortion":0.5,"rFocalLength":0.5,"rApertureSize":0.27362000000000003,"rApertureShape":0,"rApertureRotate":0},"state":{"name":"spirulae.implicit3-rt.state","iFrame":86,"width":924,"height":574,"screenCenter":{"x":0.32812050823525435,"y":0.5},"defaultScreenCenter":true,"worldSpace":true,"iTime":-1,"rz":-3.1445130209103023,"rx":-1.7179644737231015,"ry":0,"fov":0.7863406411935253,"scale":0.5474014659663254,"clipSize":[2,2,2],"rTheta":3.141592653589793,"rPhi":2.356194490192345,"renderNeeded":false,"cLatex":false,"cAutoUpdate":true,"fBasic":false,"sStep":"0.01","bLight":true,"bYup":false,"sSpp":256,"sClip":"1","cClosed":true,"sDenoise":"null","rScale1":0.549,"rScale2":0.5,"sColor":"2","sField":"0","bGrid":false,"bDiscontinuity":true,"fAppearance":false,"rOpacity":0,"rIor":1.7009999999999998,"rRoughness1":0,"rRoughness2":0.227,"rEmission1":0,"rEmission2":0,"rAbsorb1":0.80091,"rAbsorb2":0.6722100000000001,"rScatter1":0,"rScatter2":0,"rVDecayAbs":0.81016,"rVDecaySca":0.62304,"rVAbsorbHue":0.803,"rVAbsorbChr":0.4,"rVAbsorbBri":0.97,"fLighting":true,"rLightIntensity":0.5,"rLightSky":0.2,"rLightAmbient":0,"rLightSoftness":0.757243,"rLightHardness":0,"fCamera":true,"rExposure":0.5,"rCameraDistortion":0.5,"rFocalLength":0.5,"rApertureSize":0.27362000000000003,"rApertureShape":0,"rApertureRotate":0,"sLightPathDepth":"64","cMis":false,"sTonemap":"None","fAppearance1":true,"rMetallic1":1,"rTint1":1,"rVEmission1":1,"rScatterAniso1":0,"fAppearance2":true,"rBrightness2":1,"rContrast2":0.149,"rMetallic2":1,"rIor2":0.9990000000000001,"rTint2":1,"rVEmission2":0,"rScatterAniso2":0,"rVSharpSca":0,"iSpp":258.4974705078382,"sSamples":64}}
    },
    {
        "name": "Crystals",
        "id": "crystals",
        "state": {"input":"c=x+yi\nt(x,y)=max(Re(c),Re(exp(0.4pii)c),Re(exp(0.8pii)c),Re(exp(1.2pii)c),Re(exp(1.6pii)c))\nf(p,l,w)=max(t(p.x,p.y)/w-1,p.z+1.2(2t(p.x,p.y))-l,-p.z)\n\nR(p,theta,phi)=vec3(dot(vec3(cos(theta),sin(theta),0),p),dot(vec3(-cos(phi)sin(theta),cos(phi)cos(theta),sin(phi)),p),dot(vec3(sin(phi)sin(theta),-sin(phi)cos(theta),cos(phi)),p))\n\nf1=f(R(vec3(x-0.2,y-0.2,z),-0.1pi,-0.05pi),1.45,0.11)\nf2=f(R(vec3(x,y,z),0.6pi,-0.05pi),1.1,0.09)\nf3=f(R(vec3(x-0.2,y+0.1,z),0.pi,0.15pi),1.0,0.1)\nf4=f(R(vec3(x-0.2,y,z),-0.05pi,0.05pi),1.15,0.1)\nf5=f(R(vec3(x-0.4,y-0.1,z),0.3pi,0.1pi),1.2,0.1)\nf_a=min(f1,f2,f3,f4,f5)\nf_c=mix(2vec3(0,0.2,1),vec3(1,0,0.5),vec3(tanh((1.5hypot(x,y,z,0.1))^3)))\n\nh_k(k)=0.2(asin(sin(2^kx-ky))+asin(sin(2^ky+kx)))2^-k\nh=h_k(2)+h_k(3)+h_k(4)+h_k(5)\nh_c=mix(0.5vec3(1,0.5,0),vec3(1.5,0.4,0),vec3(1/(1+exp(-6h)))\n\ns(x,y,z)=min(f_a,z-h)\ns_c(x,y,z)=if(vec3((z-h)-f_a),f_c,h_c)\ns(x,y,z+0.8)\nc_rgb=s_c(x,y,z+0.8)","params":{"cLatex":false,"cAutoUpdate":true,"fBasic":false,"sStep":"0.01","bLight":false,"bYup":false,"sSpp":"256","sSamples":"1","sLightPathDepth":"64","cMis":false,"sClip":"2","cClosed":true,"sDenoise":"runet2gan2","sTonemap":"Aces","rScale1":0.38926,"rScale2":0.5,"fAppearance1":false,"sColor":"1","sField":"0","bGrid":false,"bDiscontinuity":true,"rOpacity":0,"rIor":1.7009999999999998,"rRoughness1":0,"rMetallic1":0,"rTint1":0,"rEmission1":0,"rAbsorb1":0.49995,"rVEmission1":0,"rScatter1":0,"rScatterAniso1":0,"fAppearance2":false,"rBrightness2":0.875,"rContrast2":0.2,"rRoughness2":0.227,"rMetallic2":1,"rIor2":0.9990000000000001,"rTint2":1,"rEmission2":0,"rAbsorb2":0,"rVDecayAbs":0.5004000000000001,"rVAbsorbHue":0.61,"rVAbsorbChr":0.4,"rVEmission2":0,"rScatter2":0.17523,"rScatterAniso2":-0.99,"rVDecaySca":0.5648399999999999,"rVSharpSca":0,"fLighting":false,"rTheta":-0.15707963267948966,"rPhi":1.988628149722339,"rLightIntensity":0.523,"rLightSky":0,"rLightAmbient":0,"rLightSoftness":0.31069,"rLightHardness":0,"fCamera":false,"fov":0.7863406411935253,"rExposure":0.5,"ry":0,"rCameraDistortion":0.5,"rFocalLength":0.5,"rApertureSize":0.01,"rApertureShape":0,"rApertureRotate":0},"state":{"name":"spirulae.implicit3-rt.state","iFrame":141,"sSamples":"1","iSpp":141,"width":1100,"height":295,"screenCenter":{"x":0.34362660369613396,"y":0.6865631666302445},"defaultScreenCenter":false,"worldSpace":true,"iTime":-1,"rz":1.4354869790896838,"rx":-1.9079644737231014,"ry":0,"fov":0.7863406411935253,"scale":0.5976363595906038,"clipSize":[2,2,2],"rTheta":-0.15707963267948966,"rPhi":1.988628149722339,"renderNeeded":false,"cLatex":false,"cAutoUpdate":true,"fBasic":false,"sStep":"0.01","bLight":true,"bYup":false,"sSpp":"256","sLightPathDepth":"64","cMis":false,"sClip":"2","cClosed":true,"sDenoise":"runet2gan2","sTonemap":"Aces","rScale1":0.38926,"rScale2":0.5,"fAppearance1":false,"sColor":"1","sField":"0","bGrid":false,"bDiscontinuity":true,"rOpacity":0,"rIor":1.7009999999999998,"rRoughness1":0,"rMetallic1":0,"rTint1":0,"rEmission1":0,"rAbsorb1":0.49995,"rVEmission1":0,"rScatter1":0,"rScatterAniso1":0,"fAppearance2":false,"rBrightness2":0.875,"rContrast2":0.2,"rRoughness2":0.227,"rMetallic2":1,"rIor2":0.9990000000000001,"rTint2":1,"rEmission2":0,"rAbsorb2":0,"rVDecayAbs":0.5004000000000001,"rVAbsorbHue":0.61,"rVAbsorbChr":0.4,"rVEmission2":0,"rScatter2":0.17523,"rScatterAniso2":-0.99,"rVDecaySca":0.5648399999999999,"rVSharpSca":0,"fLighting":false,"rLightIntensity":0.523,"rLightSky":0,"rLightAmbient":0,"rLightSoftness":0.31069,"rLightHardness":0,"fCamera":false,"rExposure":0.5,"rCameraDistortion":0.5,"rFocalLength":0.5,"rApertureSize":0.01,"rApertureShape":0,"rApertureRotate":0}}
    },
    {
        "name": "Among Us",
        "id": "among-us",
        "state": {"input":"# smoothed union\ns_min(x,y,k)=-ln(e^(-kx)+e^(-ky))/k\n# body parts (L^p ellipsoids)\ns_body=(1.5|y|)^3+|x|^3+(z/1.4)^4-1+0.1z\ns_leg=(1.9|y|)^3+(2.5abs(|x|-0.5))^3.5+(z+1)^2-1\ns_eyes=(4(y-0.8))^4+(1.5x)^2+(2(z-0.3))^4-1\ns_back=(3(y+0.8))^4+(1.3x)^2+(1.2(z+0.1))^4-1\n# put them together\nA(x,y,z)=s_min(s_min(s_min(s_body,s_leg,2),s_eyes,2),s_back,2)\n\nf(k)=0.4(cos(2^kx)+cos(2^ky)+cos(2^kz))2^-k\nA(x/3,y/3,z/3)=f(1)+f(2)+f(3)+f(4)+f(5)+f(6)+f(7)+f(8)+f(9)+f(10)","params":{"cLatex":false,"cAutoUpdate":true,"fBasic":false,"sStep":"0.01","bLight":true,"bYup":false,"sSpp":"256","sSamples":"16","sLightPathDepth":"64","cMis":false,"sClip":"1","cClosed":true,"sDenoise":"runet2gan2","sTonemap":"None","rScale1":0.7597,"rScale2":0.5,"fAppearance1":false,"sColor":"2","sField":"0","bGrid":false,"bDiscontinuity":true,"rOpacity":1,"rIor":3,"rRoughness1":0,"rMetallic1":1,"rTint1":1,"rEmission1":0,"rAbsorb1":0.49995,"rVEmission1":0,"rScatter1":0,"rScatterAniso1":0,"fAppearance2":true,"rBrightness2":0.875,"rContrast2":0.2,"rRoughness2":0.3,"rMetallic2":1,"rIor2":0.9990000000000001,"rTint2":1,"rEmission2":0,"rAbsorb2":0,"rVDecayAbs":0.5004000000000001,"rVAbsorbHue":0,"rVAbsorbChr":0.4,"rVEmission2":0,"rScatter2":0,"rScatterAniso2":0,"rVDecaySca":0.01,"rVSharpSca":0,"fLighting":false,"rTheta":3.141592653589793,"rPhi":2.356194490192345,"rLightIntensity":0.5,"rLightSky":0.2,"rLightAmbient":0,"rLightSoftness":0.836164,"rLightHardness":0,"fCamera":false,"fov":0.7863406411935253,"rExposure":0.5,"ry":0,"rCameraDistortion":0.5,"rFocalLength":0.5,"rApertureSize":0.47452,"rApertureShape":0,"rApertureRotate":0},"state":{"name":"spirulae.implicit3-rt.state","iFrame":16,"sSamples":"16","iSpp":256,"width":924,"height":604,"screenCenter":{"x":0.4926948051948052,"y":0.5769867549668876},"defaultScreenCenter":true,"worldSpace":true,"iTime":-1,"rz":-8.974513020910313,"rx":-1.7779644737230997,"ry":0,"fov":0.7863406411935253,"scale":0.5211211116169913,"clipSize":[2,2,2],"rTheta":3.141592653589793,"rPhi":2.356194490192345,"renderNeeded":false,"cLatex":false,"cAutoUpdate":true,"fBasic":false,"sStep":"0.01","bLight":true,"bYup":false,"sSpp":"256","sLightPathDepth":"64","cMis":false,"sClip":"1","cClosed":true,"sDenoise":"runet2gan2","sTonemap":"None","rScale1":0.7597,"rScale2":0.5,"fAppearance1":false,"sColor":"2","sField":"0","bGrid":false,"bDiscontinuity":true,"rOpacity":1,"rIor":3,"rRoughness1":0,"rMetallic1":1,"rTint1":1,"rEmission1":0,"rAbsorb1":0.49995,"rVEmission1":0,"rScatter1":0,"rScatterAniso1":0,"fAppearance2":true,"rBrightness2":0.875,"rContrast2":0.2,"rRoughness2":0.3,"rMetallic2":1,"rIor2":0.9990000000000001,"rTint2":1,"rEmission2":0,"rAbsorb2":0,"rVDecayAbs":0.5004000000000001,"rVAbsorbHue":0,"rVAbsorbChr":0.4,"rVEmission2":0,"rScatter2":0,"rScatterAniso2":0,"rVDecaySca":0.01,"rVSharpSca":0,"fLighting":false,"rLightIntensity":0.5,"rLightSky":0.2,"rLightAmbient":0,"rLightSoftness":0.836164,"rLightHardness":0,"fCamera":false,"rExposure":0.5,"rCameraDistortion":0.5,"rFocalLength":0.5,"rApertureSize":0.47452,"rApertureShape":0,"rApertureRotate":0}}
    },
    {
        "name": "Glass Bunny",
        "id": "glass-bunny",
        "state": {"input":"a_{0}=\\sin\\left(-0.829576x+2.602273y-1.618450z-0.715234\\right)\na_{1}=\\sin\\left(-2.000557x+0.426287y+2.305181z+1.273059\\right)\na_{2}=\\sin\\left(0.620192x-0.666695y-1.330159z-0.866385\\right)\na_{3}=\\sin\\left(0.514423x-3.679781y-1.425561z+1.489850\\right)\na_{4}=\\sin\\left(-1.928094x-0.824546y-2.510200z-0.417734\\right)\na_{5}=\\sin\\left(-0.484261x+1.788136y-0.642165z+0.200334\\right)\na_{6}=\\sin\\left(1.136995x+1.867003y-1.961205z+0.976892\\right)\na_{7}=\\sin\\left(2.864650x-0.199419y+1.506638z-1.095236\\right)\nb_{0}=\\sin\\left(1.812220a_{0}+2.884073a_{1}+3.612080a_{2}-1.995718a_{3}-0.377214a_{4}+1.020748a_{5}-1.627613a_{6}-0.287912a_{7}+2.026797\\right)\nb_{1}=\\sin\\left(-0.455035a_{0}-2.222596a_{1}-2.399931a_{2}+0.744959a_{3}+1.492880a_{4}-0.757997a_{5}+1.792551a_{6}-0.479629a_{7}-0.111036\\right)\nb_{2}=\\sin\\left(-0.808131a_{0}-0.291558a_{1}+1.988618a_{2}+0.728573a_{3}+0.274100a_{4}+0.064004a_{5}+0.330706a_{6}+0.754427a_{7}-0.057252\\right)\nb_{3}=\\sin\\left(0.920693a_{0}-0.735599a_{1}-0.422997a_{2}-0.553346a_{3}+0.006058a_{4}-0.597446a_{5}-0.042582a_{6}+1.127596a_{7}-2.628315\\right)\nb_{4}=\\sin\\left(1.769702a_{0}+1.713630a_{1}+4.831693a_{2}+0.631755a_{3}-0.125397a_{4}-2.731953a_{5}+1.208619a_{6}-0.935535a_{7}+1.892788\\right)\nb_{5}=\\sin\\left(2.722546a_{0}+0.674967a_{1}+6.130916a_{2}-0.066449a_{3}-2.537350a_{4}-1.382229a_{5}-0.689582a_{6}-0.396771a_{7}+0.344579\\right)\nb_{6}=\\sin\\left(0.430195a_{0}-1.879223a_{1}-1.190848a_{2}-0.102922a_{3}-1.021774a_{4}+2.600340a_{5}+0.510470a_{6}-1.503284a_{7}+0.705401\\right)\nb_{7}=\\sin\\left(1.772722a_{0}-4.550030a_{1}-8.405404a_{2}-1.069012a_{3}+1.129655a_{4}-1.301046a_{5}+1.491861a_{6}-2.341415a_{7}-2.812914\\right)\n4.161703b_{0}-12.431351b_{1}-11.705051b_{2}-19.281926b_{3}-7.128351b_{4}+6.133010b_{5}-5.526843b_{6}-2.767950b_{7}+17.549847","params":{"cLatex":false,"cAutoUpdate":true,"fBasic":false,"sStep":"0.01","bLight":true,"bYup":false,"sSpp":"256","sSamples":"16","sLightPathDepth":"64","cMis":false,"sClip":"2","cClosed":true,"sDenoise":"runet2gan2","sTonemap":"None","rScale1":0.35888,"rScale2":0.5,"fAppearance1":false,"sColor":"2","sField":"0","bGrid":false,"bDiscontinuity":true,"rOpacity":0,"rIor":1.7009999999999998,"rRoughness1":0.139,"rMetallic1":0,"rTint1":0,"rEmission1":0,"rAbsorb1":0.35739,"rVEmission1":0,"rScatter1":0,"rScatterAniso1":0,"fAppearance2":true,"rBrightness2":0.875,"rContrast2":0.2,"rRoughness2":0.3,"rMetallic2":1,"rIor2":0.9990000000000001,"rTint2":1,"rEmission2":0,"rAbsorb2":0,"rVDecayAbs":0.5004000000000001,"rVAbsorbHue":0,"rVAbsorbChr":0.4,"rVEmission2":0,"rScatter2":0,"rScatterAniso2":0,"rVDecaySca":0.01,"rVSharpSca":0,"fLighting":true,"rTheta":1.6399113651738721,"rPhi":2.356194490192345,"rLightIntensity":0.539,"rLightSky":0,"rLightAmbient":0,"rLightSoftness":1,"rLightHardness":0,"fCamera":true,"fov":0.7863406411935253,"rExposure":0.5,"ry":0,"rCameraDistortion":0.5,"rFocalLength":0.5,"rApertureSize":0.4,"rApertureShape":0,"rApertureRotate":0},"state":{"name":"spirulae.implicit3-rt.state","iFrame":16,"width":924,"height":574,"screenCenter":{"x":0.49756493506493504,"y":0.5326655052264808},"defaultScreenCenter":true,"worldSpace":true,"iTime":-1,"rz":-6.0245130209103035,"rx":-1.1779644737231012,"ry":0,"fov":0.7863406411935253,"scale":0.6958836484943821,"clipSize":[2,2,2],"rTheta":1.6399113651738721,"rPhi":2.356194490192345,"renderNeeded":false,"cLatex":false,"cAutoUpdate":true,"fBasic":false,"sStep":"0.01","bLight":true,"bYup":false,"sSpp":256,"sClip":"2","cClosed":true,"sDenoise":"runet2gan2","rScale1":0.35888,"rScale2":0.5,"sColor":"2","sField":"0","bGrid":false,"bDiscontinuity":true,"fAppearance":false,"rOpacity":0,"rIor":1.7009999999999998,"rRoughness1":0.139,"rRoughness2":0.3,"rEmission1":0,"rEmission2":0,"rAbsorb1":0.35739,"rAbsorb2":0,"rScatter1":0,"rScatter2":0,"rVDecayAbs":0.5004000000000001,"rVDecaySca":0.01,"rVAbsorbHue":0,"rVAbsorbChr":0.4,"rVAbsorbBri":0.97,"fLighting":true,"rLightIntensity":0.539,"rLightSky":0,"rLightAmbient":0,"rLightSoftness":1,"rLightHardness":0,"fCamera":true,"rExposure":0.5,"rCameraDistortion":0.5,"rFocalLength":0.5,"rApertureSize":0.4,"rApertureShape":0,"rApertureRotate":0,"sLightPathDepth":"64","cMis":false,"sTonemap":"None","fAppearance1":false,"rMetallic1":0,"rTint1":0,"rVEmission1":0,"rScatterAniso1":0,"fAppearance2":true,"rBrightness2":0.875,"rContrast2":0.2,"rMetallic2":1,"rIor2":0.9990000000000001,"rTint2":1,"rVEmission2":0,"rScatterAniso2":0,"rVSharpSca":0,"iSpp":256,"sSamples":"16"}}
    },
    {
        "name": "Bull's Head",
        "id": "bull-head",
        "state": {"input":"cos(3x)cos(3y)+sin(3y)cos(3z)-asin(sin(3z)cos(3x))+x^2+y^2+z^2-1","params":{"cLatex":true,"cAutoUpdate":true,"fBasic":false,"sStep":"0.01","bLight":true,"bYup":false,"sSpp":"256","sSamples":"1","sLightPathDepth":"128","cMis":false,"sClip":"1","cClosed":true,"sDenoise":"runet2gan2","sTonemap":"None","rScale1":0.5,"rScale2":0.5,"fAppearance1":false,"sColor":"2","sField":"0","bGrid":false,"bDiscontinuity":true,"rOpacity":0,"rIor":1.7009999999999998,"rRoughness1":0,"rMetallic1":1,"rTint1":1,"rEmission1":0,"rAbsorb1":0.49995,"rVEmission1":0,"rScatter1":0.26433,"rScatterAniso1":0,"fAppearance2":false,"rBrightness2":0.875,"rContrast2":0.2,"rRoughness2":0.3,"rMetallic2":1,"rIor2":0.9990000000000001,"rTint2":1,"rEmission2":0,"rAbsorb2":0,"rVDecayAbs":0.26280000000000003,"rVAbsorbHue":0.296,"rVAbsorbChr":0.4,"rVEmission2":0,"rScatter2":0.98802,"rScatterAniso2":0,"rVDecaySca":0.038130000000000004,"rVSharpSca":0,"fLighting":false,"rTheta":-0.546637121724624,"rPhi":2.356194490192345,"rLightIntensity":0.5,"rLightSky":0.2,"rLightAmbient":0,"rLightSoftness":0.625375,"rLightHardness":0,"fCamera":false,"fov":0.6884800300342032,"rExposure":0.5,"ry":0,"rCameraDistortion":0.5,"rFocalLength":0.5,"rApertureSize":0.01,"rApertureShape":0,"rApertureRotate":0},"state":{"name":"spirulae.implicit3-rt.state","iFrame":54,"width":924,"height":604,"screenCenter":{"x":0.32812050823525435,"y":0.5},"defaultScreenCenter":true,"worldSpace":true,"iTime":-1,"rz":3.435486979089686,"rx":-1.7179644737231,"ry":0,"fov":0.6884800300342032,"scale":0.5882679107490744,"clipSize":[2,2,2],"rTheta":-0.546637121724624,"rPhi":2.356194490192345,"renderNeeded":false,"cLatex":false,"cAutoUpdate":true,"fBasic":false,"sStep":"0.01","bLight":true,"bYup":false,"sSpp":"256","sLightPathDepth":"128","sClip":"1","cClosed":true,"sDenoise":"runet2gan2","sTonemap":"None","rScale1":0.5,"rScale2":0.5,"sColor":"2","sField":"0","bGrid":false,"bDiscontinuity":true,"fAppearance1":false,"rOpacity":0,"rIor":1.7009999999999998,"rRoughness1":0,"rEmission1":0,"rAbsorb1":0.49995,"rVEmission1":0,"rScatter1":0.26433,"rScatterAniso1":0,"fAppearance2":false,"rRoughness2":0.3,"rEmission2":0,"rAbsorb2":0,"rVDecayAbs":0.26280000000000003,"rVAbsorbHue":0.296,"rVAbsorbChr":0.4,"rScatter2":0.98802,"rScatterAniso2":0,"rVDecaySca":0.038130000000000004,"rVSharpSca":0,"fLighting":false,"rLightIntensity":0.5,"rLightSky":0.2,"rLightAmbient":0,"rLightSoftness":0.625375,"rLightHardness":0,"fCamera":false,"rExposure":0.5,"rCameraDistortion":0.5,"rFocalLength":0.5,"rApertureSize":0.01,"rApertureShape":0,"rApertureRotate":0,"sSamples":"1","cMis":false,"rMetallic1":1,"rTint1":1,"rBrightness2":0.875,"rContrast2":0.2,"rMetallic2":1,"rIor2":0.9990000000000001,"rTint2":1,"rVEmission2":0,"iSpp":54}}
    }
];


document.body.onload = function (event) {
    console.log("onload");

    for (var i = 0; i < builtinStates.length; i++) {
        var state = builtinStates[i].state.state;
        var params = builtinStates[i].state.params;
        params.fBasic = (window.innerWidth <= 480 && window.innerHeight <= 960);
        params.fAppearance1 = params.fAppearance2 = params.fCamera = params.fLighting = true;
        params.sDenoise = "null";
        // params.sDenoise = "runet2gan";
        params.sSpp = state.sSpp = 256;
        state.iSpp = 0.0;
        params.sSamples = state.sSamples = "20";
        // state.defaultScreenCenter = true;
    }
    initBuiltInStates(builtinStates);

    // init parser
    BuiltInMathFunctions.initMathFunctions(
        BuiltInMathFunctions.rawMathFunctionsShared
            .concat(BuiltInMathFunctions.rawMathFunctionsR)
    );
    MathParser.DependentVariables = {
        0: {
            'val': true,
        },
        1: {
            'val': true,
            'c_rgb': true,
        },
        2: {
            'val': true,
            'c_hsv': true,
        },
        3: {
            'val': true,
            'c_hsl': true,
        },
        'c_rgb': { type: 'vec3' },
        'c_hsv': { type: 'vec3' },
        'c_hsl': { type: 'vec3' },
    };

    CodeGenerator.langs.glsl.config = {
        fun: [
            "float {%funname%}(float x, float y, float z) {\n\
    float {%funbody%};\n\
    return {%val%};\n\
}",
            "#define CUSTOM_COLOR rgb2rgb\n\
float {%funname%}(float x, float y, float z) {\n\
    float {%funbody%};\n\
    return {%val%};\n\
}\n\
vec3 {%funname%}Color(float x, float y, float z) {\n\
    float {%funbody%};\n\
    return vec3({%c_rgb[0]%},{%c_rgb[1]%},{%c_rgb[2]%});\n\
}",
            "#define CUSTOM_COLOR hsv2rgb\n\
float {%funname%}(float x, float y, float z) {\n\
    float {%funbody%};\n\
    return {%val%};\n\
}\n\
vec3 {%funname%}Color(float x, float y, float z) {\n\
    float {%funbody%};\n\
    return vec3({%c_hsv[0]%},{%c_hsv[1]%},{%c_hsv[2]%});\n\
}",
            "#define CUSTOM_COLOR hsl2rgb\n\
float {%funname%}(float x, float y, float z) {\n\
    float {%funbody%};\n\
    return {%val%};\n\
}\n\
vec3 {%funname%}Color(float x, float y, float z) {\n\
    float {%funbody%};\n\
    return vec3({%c_hsl[0]%},{%c_hsl[1]%},{%c_hsl[2]%});\n\
}",
        ],
        prefix: 'v',
        def: "{%varname%}={%expr%}",
        joiner: ", "
    };

    // init parameters
    initParameters([
        new GraphingParameter("cLatex", "checkbox-latex"),
        new GraphingParameter("cAutoUpdate", "checkbox-auto-compile"),
        // basic
        new ParameterFolder("fBasic", "folder-basic"),
        new GraphingParameter("sStep", "select-step"),
        new GraphingParameter("bLight", "checkbox-light"),
        new GraphingParameter("bYup", "checkbox-yup"),
        new GraphingParameter("sSpp", "select-spp"),
        new GraphingParameter("sSamples", "select-samples"),
        new GraphingParameter("sLightPathDepth", "select-light-path-depth"),
        new GraphingParameter("cMis", "checkbox-mis"),
        new GraphingParameter("sClip", "select-clip"),
        new GraphingParameter("cClosed", "checkbox-closed"),
        new GraphingParameter("sDenoise", "select-denoise"),
        new GraphingParameter("sTonemap", "select-tonemap"),
        new UniformSlider("rScale1", "slider-scale1", 0.01, 0.99, 0.5),
        new UniformSlider("rScale2", "slider-scale2", 0.01, 0.99, 0.5),
        // object appearance
        new ParameterFolder("fAppearance1", "folder-object"),
        new GraphingParameter("sColor", "select-color"),
        new GraphingParameter("sField", "select-field"),
        new GraphingParameter("bGrid", "checkbox-grid"),
        new GraphingParameter("bDiscontinuity", "checkbox-discontinuity"),
        new UniformSlider("rOpacity", "slider-opacity", 0, 1, 0.0),
        new UniformSlider("rIor", "slider-ior", 0, 3, 1.7),
        new UniformSlider("rRoughness1", "slider-roughness1", 0, 1, 0.2),
        new UniformSlider("rMetallic1", "slider-metallic1", 0, 1, 1.0),
        // new UniformSlider("rDiffuse1", "slider-diffuse1", 0, 1, 0.0),
        new UniformSlider("rTint1", "slider-tint1", 0, 1, 1.0),
        new UniformSlider("rEmission1", "slider-emission1", 0, 1, 0.0),
        new UniformSlider("rAbsorb1", "slider-absorb1", 0, 0.99, 0.5),
        new UniformSlider("rVEmission1", "slider-vemission1", 0, 1, 0.0),
        new UniformSlider("rScatter1", "slider-scatter1", 0, 0.99, 0.0),
        new UniformSlider("rScatterAniso1", "slider-scatter-aniso1", -0.99, 0.99, 0.0),
        // background appearance
        new ParameterFolder("fAppearance2", "folder-background"),
        new UniformSlider("rBrightness2", "slider-brightness2", 0, 1, 0.875),
        new UniformSlider("rContrast2", "slider-contrast2", 0, 1, 0.2),
        new UniformSlider("rRoughness2", "slider-roughness2", 0, 1, 0.3),
        new UniformSlider("rMetallic2", "slider-metallic2", 0, 1, 1.0),
        new UniformSlider("rIor2", "slider-ior2", 0, 3, 1.0),
        new UniformSlider("rTint2", "slider-tint2", 0, 1, 1.0),
        new UniformSlider("rEmission2", "slider-emission2", 0, 1, 0.0),
        new UniformSlider("rAbsorb2", "slider-absorb2", 0, 0.99, 0.0),
        new UniformSlider("rVDecayAbs", "slider-vdecay-abs", 0.1, 0.98, 0.5),
        new UniformSlider("rVAbsorbHue", "slider-vabsorb-hue", 0, 1, 0.55),
        new UniformSlider("rVAbsorbChr", "slider-vabsorb-chr", 0, 1, 0.4),
        new UniformSlider("rVEmission2", "slider-vemit", 0, 1, 0),
        // new UniformSlider("rVEmissionTint2", "slider-vemit-tint", 0, 1, 0.0),
        new UniformSlider("rScatter2", "slider-scatter2", 0, 0.99, 0.0),
        new UniformSlider("rScatterAniso2", "slider-scatter-aniso2", -0.99, 0.99, 0.0),
        new UniformSlider("rVDecaySca", "slider-vdecay-sca", 0.01, 0.98, 0.5),
        new UniformSlider("rVSharpSca", "slider-vsharp-sca", 0, 1, 0),
        // lighting
        new ParameterFolder("fLighting", "folder-lighting"),
        new UniformSlider("rTheta", "slider-theta", -0.5 * Math.PI, 1.5 * Math.PI, 1.0 * Math.PI),
        new UniformSlider("rPhi", "slider-phi", 0, Math.PI, 0.75 * Math.PI),
        new UniformSlider("rLightIntensity", "slider-light-intensity", 0, 1, 0.5),
        new UniformSlider("rLightSky", "slider-light-sky", 0, 1, 0.2),
        new UniformSlider("rLightAmbient", "slider-light-ambient", 0, 1, 0.0),
        new UniformSlider("rLightSoftness", "slider-light-softness", 0.001, 1, 0.8),
        new UniformSlider("rLightHardness", "slider-light-hardness", 0, 1, 0.0),
        // camera
        new ParameterFolder("fCamera", "folder-camera"),
        new UniformSlider("fov", "slider-camera-fov", 0.01*Math.PI, 0.9*Math.PI, 0.25*Math.PI),
        new UniformSlider("rExposure", "slider-camera-exposure", 0.01, 0.99, 0.5),
        new UniformSlider("ry", "slider-camera-roll", -Math.PI, Math.PI, 0.0),
        new UniformSlider("rCameraDistortion", "slider-camera-distortion", 0.0, 1.0, 0.5),
        new UniformSlider("rFocalLength", "slider-focal-length", 0.01, 0.99, 0.5),
        new UniformSlider("rApertureSize", "slider-aperture-size", 0.01, 0.99, 0.01),
        new UniformSlider("rApertureShape", "slider-aperture-shape", 0.0, 1.0, 0.0),
        new UniformSlider("rApertureRotate", "slider-aperture-rotate", -Math.PI, Math.PI, 0.0),
    ]);
    UpdateFunctionInputConfig.complexMode = false;
    UpdateFunctionInputConfig.implicitMode = true;
    UpdateFunctionInputConfig.warnNaN = true;

    // denoise
    let selectDenoise = document.getElementById("select-denoise");
    useDenoiser(selectDenoise.value);
    selectDenoise.addEventListener("input",
        () => useDenoiser(selectDenoise.value));

    // init viewport
    // resetState({
    //     rz: -0.95 * Math.PI,
    //     rx: -0.48 * Math.PI,
    //     scale: 0.3,
    //     clipSize: [2.0, 2.0, 2.0]
    // }, false);

    // main
    initMain([
        "frag-render.glsl",
        "sky_model.glsl",
        "../shaders/frag-copy.glsl",
        "../shaders/frag-rt-post.glsl",
        "../shaders/frag-rt-expand.glsl",
        "../shaders/frag-tonemap.glsl",
        "../shaders/dnn-conv2d-3.glsl",
        "../shaders/dnn-conv2d110.glsl",
        "../shaders/dnn-convtranspose2d421.glsl",
        "../shaders/complex-zeta.glsl",
        "../shaders/complex.glsl",
    ]);
};



// for local testing

function exportAllFunctions(lang, grad = false) {
    let langpack = CodeGenerator.langs[lang];
    let oldConfig = langpack.config;
    langpack.config = langpack.presets[grad ? 'implicit3g_compact' : 'implicit3_compact'];
    var funs = builtinFunctions;
    var names = [], exprs = [];
    for (var i = 0; i < funs.length; i++) {
        var name = 'fun' + funs[i][0].replace(/[^\w]/g, '');
        console.log(name);
        var str = funs[i][1].replaceAll("&#32;", ' ')
        var expr = MathParser.parseInput(str);
        names.push(name);
        exprs.push({ val: expr.val[0] });
    }
    var res = CodeGenerator.postfixToSource(exprs, names, lang);
    console.log(res.source);
    langpack.config = oldConfig;
}

function exportCurrentFunction(lang, grad = false) {
    let langpack = CodeGenerator.langs[lang];
    let oldConfig = langpack.config;
    langpack.config = langpack.presets[grad ? 'implicit3g' : 'implicit3'];
    var str = document.getElementById("equation-input").value;
    var expr = MathParser.parseInput(str).val[0];
    var res = CodeGenerator.postfixToSource(
        [{ val: expr }], ["fun"], lang);
    console.log(res.source);
    langpack.config = oldConfig;
}
