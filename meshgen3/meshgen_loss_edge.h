/* This file was automatically generated by CasADi 3.6.3.
 *  It consists of: 
 *   1) content generated by CasADi runtime: not copyrighted
 *   2) template code copied from CasADi source: permissively licensed (MIT-0)
 *   3) user code: owned by the user
 *
 */
#ifdef __cplusplus
extern "C" {
#endif

/* How to prefix internal symbols */
#ifdef CASADI_CODEGEN_PREFIX
  #define CASADI_NAMESPACE_CONCAT(NS, ID) _CASADI_NAMESPACE_CONCAT(NS, ID)
  #define _CASADI_NAMESPACE_CONCAT(NS, ID) NS ## ID
  #define CASADI_PREFIX(ID) CASADI_NAMESPACE_CONCAT(CODEGEN_PREFIX, ID)
#else
  #define CASADI_PREFIX(ID) meshgen_loss_edge_ ## ID
#endif

#include <math.h>

#ifndef casadi_real
#define casadi_real float
#endif

#ifndef casadi_int
#define casadi_int long long int
#endif

/* Add prefix to internal symbols */
#define casadi_f0 CASADI_PREFIX(f0)
#define casadi_s0 CASADI_PREFIX(s0)
#define casadi_s1 CASADI_PREFIX(s1)
#define casadi_s2 CASADI_PREFIX(s2)
#define casadi_sq CASADI_PREFIX(sq)

/* Symbol visibility in DLLs */
#ifndef CASADI_SYMBOL_EXPORT
  #if defined(_WIN32) || defined(__WIN32__) || defined(__CYGWIN__)
    #if defined(STATIC_LINKED)
      #define CASADI_SYMBOL_EXPORT
    #else
      #define CASADI_SYMBOL_EXPORT __declspec(dllexport)
    #endif
  #elif defined(__GNUC__) && defined(GCC_HASCLASSVISIBILITY)
    #define CASADI_SYMBOL_EXPORT __attribute__ ((visibility ("default")))
  #else
    #define CASADI_SYMBOL_EXPORT
  #endif
#endif

casadi_real casadi_sq(casadi_real x) { return x*x;}

static const casadi_int casadi_s0[16] = {12, 1, 0, 12, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11};
static const casadi_int casadi_s1[5] = {1, 1, 0, 1, 0};
static const casadi_int casadi_s2[27] = {1, 12, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0};

/* meshgen_loss_edge:(i0[12])->(o0,o1[1x12],o2) */
static int casadi_f0(const casadi_real** arg, casadi_real** res, casadi_int* iw, casadi_real* w, int mem) {
  casadi_real a0, a1, a10, a11, a12, a13, a14, a15, a16, a17, a18, a19, a2, a20, a21, a22, a23, a24, a25, a26, a27, a28, a29, a3, a30, a31, a32, a33, a34, a35, a36, a37, a38, a39, a4, a40, a41, a42, a43, a5, a6, a7, a8, a9;
  a0=1.0000009999999999e+00;
  a1=arg[0]? arg[0][4] : 0;
  a2=arg[0]? arg[0][1] : 0;
  a3=(a1-a2);
  a4=arg[0]? arg[0][0] : 0;
  a5=arg[0]? arg[0][3] : 0;
  a6=(a4+a5);
  a7=arg[0]? arg[0][6] : 0;
  a6=(a6+a7);
  a8=arg[0]? arg[0][9] : 0;
  a6=(a6+a8);
  a9=4.;
  a6=(a6/a9);
  a10=(a4-a6);
  a11=casadi_sq(a10);
  a12=(a2+a1);
  a13=arg[0]? arg[0][7] : 0;
  a12=(a12+a13);
  a14=arg[0]? arg[0][10] : 0;
  a12=(a12+a14);
  a12=(a12/a9);
  a15=(a2-a12);
  a16=casadi_sq(a15);
  a11=(a11+a16);
  a16=arg[0]? arg[0][2] : 0;
  a17=arg[0]? arg[0][5] : 0;
  a18=(a16+a17);
  a19=arg[0]? arg[0][8] : 0;
  a18=(a18+a19);
  a20=arg[0]? arg[0][11] : 0;
  a18=(a18+a20);
  a18=(a18/a9);
  a21=(a16-a18);
  a22=casadi_sq(a21);
  a11=(a11+a22);
  a22=(a5-a6);
  a23=casadi_sq(a22);
  a11=(a11+a23);
  a1=(a1-a12);
  a23=casadi_sq(a1);
  a11=(a11+a23);
  a23=(a17-a18);
  a24=casadi_sq(a23);
  a11=(a11+a24);
  a24=(a7-a6);
  a25=casadi_sq(a24);
  a11=(a11+a25);
  a25=(a13-a12);
  a26=casadi_sq(a25);
  a11=(a11+a26);
  a26=(a19-a18);
  a27=casadi_sq(a26);
  a11=(a11+a27);
  a6=(a8-a6);
  a27=casadi_sq(a6);
  a11=(a11+a27);
  a12=(a14-a12);
  a27=casadi_sq(a12);
  a11=(a11+a27);
  a18=(a20-a18);
  a27=casadi_sq(a18);
  a11=(a11+a27);
  a27=sqrt(a11);
  a3=(a3/a27);
  a19=(a19-a16);
  a19=(a19/a27);
  a28=(a3*a19);
  a17=(a17-a16);
  a17=(a17/a27);
  a13=(a13-a2);
  a13=(a13/a27);
  a29=(a17*a13);
  a28=(a28-a29);
  a29=casadi_sq(a28);
  a7=(a7-a4);
  a7=(a7/a27);
  a30=(a17*a7);
  a5=(a5-a4);
  a5=(a5/a27);
  a31=(a5*a19);
  a30=(a30-a31);
  a31=casadi_sq(a30);
  a29=(a29+a31);
  a31=(a5*a13);
  a32=(a3*a7);
  a31=(a31-a32);
  a32=casadi_sq(a31);
  a29=(a29+a32);
  a29=sqrt(a29);
  a32=(a28/a29);
  a14=(a14-a2);
  a14=(a14/a27);
  a2=(a14*a17);
  a20=(a20-a16);
  a20=(a20/a27);
  a16=(a20*a3);
  a2=(a2-a16);
  a16=casadi_sq(a2);
  a33=(a20*a5);
  a8=(a8-a4);
  a8=(a8/a27);
  a4=(a8*a17);
  a33=(a33-a4);
  a4=casadi_sq(a33);
  a16=(a16+a4);
  a4=(a8*a3);
  a34=(a14*a5);
  a4=(a4-a34);
  a34=casadi_sq(a4);
  a16=(a16+a34);
  a16=sqrt(a16);
  a34=(a2/a16);
  a35=(a32-a34);
  a36=casadi_sq(a35);
  a37=(a30/a29);
  a38=(a33/a16);
  a39=(a37-a38);
  a40=casadi_sq(a39);
  a36=(a36+a40);
  a40=(a31/a29);
  a41=(a4/a16);
  a42=(a40-a41);
  a43=casadi_sq(a42);
  a36=(a36+a43);
  a36=(a36/a9);
  a0=(a0-a36);
  a36=log(a0);
  a36=(-a36);
  if (res[0]!=0) res[0][0]=a36;
  a36=2.5000000000000000e-01;
  a6=(a6+a6);
  a9=(a8/a27);
  a4=(a4+a4);
  a41=(a41/a16);
  a42=(a42+a42);
  a0=(a36/a0);
  a42=(a42*a0);
  a41=(a41*a42);
  a38=(a38/a16);
  a39=(a39+a39);
  a39=(a39*a0);
  a38=(a38*a39);
  a41=(a41+a38);
  a34=(a34/a16);
  a35=(a35+a35);
  a35=(a35*a0);
  a34=(a34*a35);
  a41=(a41+a34);
  a34=(a16+a16);
  a41=(a41/a34);
  a4=(a4*a41);
  a34=(a42/a16);
  a4=(a4-a34);
  a34=(a3*a4);
  a33=(a33+a33);
  a33=(a33*a41);
  a0=(a39/a16);
  a33=(a33-a0);
  a0=(a17*a33);
  a34=(a34-a0);
  a9=(a9*a34);
  a0=(a20/a27);
  a38=(a5*a33);
  a2=(a2+a2);
  a2=(a2*a41);
  a16=(a35/a16);
  a2=(a2-a16);
  a16=(a3*a2);
  a38=(a38-a16);
  a0=(a0*a38);
  a9=(a9+a0);
  a0=(a14/a27);
  a16=(a17*a2);
  a41=(a5*a4);
  a16=(a16-a41);
  a0=(a0*a16);
  a9=(a9+a0);
  a0=(a5/a27);
  a41=(a20*a33);
  a43=(a14*a4);
  a41=(a41-a43);
  a43=(a42/a29);
  a31=(a31+a31);
  a40=(a40/a29);
  a40=(a40*a42);
  a37=(a37/a29);
  a37=(a37*a39);
  a40=(a40+a37);
  a32=(a32/a29);
  a32=(a32*a35);
  a40=(a40+a32);
  a32=(a29+a29);
  a40=(a40/a32);
  a31=(a31*a40);
  a43=(a43-a31);
  a31=(a13*a43);
  a41=(a41+a31);
  a39=(a39/a29);
  a30=(a30+a30);
  a30=(a30*a40);
  a39=(a39-a30);
  a30=(a19*a39);
  a41=(a41-a30);
  a0=(a0*a41);
  a9=(a9+a0);
  a0=(a7/a27);
  a30=(a17*a39);
  a31=(a3*a43);
  a30=(a30-a31);
  a0=(a0*a30);
  a9=(a9+a0);
  a0=(a13/a27);
  a31=(a5*a43);
  a35=(a35/a29);
  a28=(a28+a28);
  a28=(a28*a40);
  a35=(a35-a28);
  a28=(a17*a35);
  a31=(a31-a28);
  a0=(a0*a31);
  a9=(a9+a0);
  a17=(a17/a27);
  a14=(a14*a2);
  a33=(a8*a33);
  a14=(a14-a33);
  a33=(a7*a39);
  a14=(a14+a33);
  a13=(a13*a35);
  a14=(a14-a13);
  a17=(a17*a14);
  a9=(a9+a17);
  a17=(a19/a27);
  a13=(a3*a35);
  a5=(a5*a39);
  a13=(a13-a5);
  a17=(a17*a13);
  a9=(a9+a17);
  a3=(a3/a27);
  a8=(a8*a4);
  a20=(a20*a2);
  a8=(a8-a20);
  a7=(a7*a43);
  a8=(a8-a7);
  a19=(a19*a35);
  a8=(a8+a19);
  a3=(a3*a8);
  a9=(a9+a3);
  a3=(a27+a27);
  a9=(a9/a3);
  a6=(a6*a9);
  a24=(a24+a24);
  a24=(a24*a9);
  a3=(a6+a24);
  a22=(a22+a22);
  a22=(a22*a9);
  a3=(a3+a22);
  a10=(a10+a10);
  a10=(a10*a9);
  a3=(a3+a10);
  a3=(a36*a3);
  a34=(a34/a27);
  a41=(a41/a27);
  a19=(a34+a41);
  a30=(a30/a27);
  a19=(a19+a30);
  a19=(a19+a10);
  a19=(a3-a19);
  if (res[1]!=0) res[1][0]=a19;
  a12=(a12+a12);
  a12=(a12*a9);
  a25=(a25+a25);
  a25=(a25*a9);
  a19=(a12+a25);
  a1=(a1+a1);
  a1=(a1*a9);
  a19=(a19+a1);
  a15=(a15+a15);
  a15=(a15*a9);
  a19=(a19+a15);
  a19=(a36*a19);
  a16=(a16/a27);
  a31=(a31/a27);
  a10=(a16+a31);
  a10=(a10+a15);
  a10=(a19-a10);
  a8=(a8/a27);
  a10=(a10-a8);
  if (res[1]!=0) res[1][1]=a10;
  a18=(a18+a18);
  a18=(a18*a9);
  a26=(a26+a26);
  a26=(a26*a9);
  a10=(a18+a26);
  a23=(a23+a23);
  a23=(a23*a9);
  a10=(a10+a23);
  a21=(a21+a21);
  a21=(a21*a9);
  a10=(a10+a21);
  a36=(a36*a10);
  a38=(a38/a27);
  a14=(a14/a27);
  a10=(a38+a14);
  a13=(a13/a27);
  a10=(a10+a13);
  a10=(a10+a21);
  a10=(a36-a10);
  if (res[1]!=0) res[1][2]=a10;
  a41=(a41-a22);
  a41=(a41+a3);
  if (res[1]!=0) res[1][3]=a41;
  a1=(a19-a1);
  a1=(a1+a8);
  if (res[1]!=0) res[1][4]=a1;
  a14=(a14-a23);
  a14=(a14+a36);
  if (res[1]!=0) res[1][5]=a14;
  a30=(a30-a24);
  a30=(a30+a3);
  if (res[1]!=0) res[1][6]=a30;
  a31=(a31-a25);
  a31=(a31+a19);
  if (res[1]!=0) res[1][7]=a31;
  a13=(a13-a26);
  a13=(a13+a36);
  if (res[1]!=0) res[1][8]=a13;
  a34=(a34-a6);
  a34=(a34+a3);
  if (res[1]!=0) res[1][9]=a34;
  a16=(a16-a12);
  a16=(a16+a19);
  if (res[1]!=0) res[1][10]=a16;
  a38=(a38-a18);
  a38=(a38+a36);
  if (res[1]!=0) res[1][11]=a38;
  if (res[2]!=0) res[2][0]=a11;
  return 0;
}

CASADI_SYMBOL_EXPORT int meshgen_loss_edge(const casadi_real** arg, casadi_real** res, casadi_int* iw, casadi_real* w, int mem){
  return casadi_f0(arg, res, iw, w, mem);
}

CASADI_SYMBOL_EXPORT int meshgen_loss_edge_alloc_mem(void) {
  return 0;
}

CASADI_SYMBOL_EXPORT int meshgen_loss_edge_init_mem(int mem) {
  return 0;
}

CASADI_SYMBOL_EXPORT void meshgen_loss_edge_free_mem(int mem) {
}

CASADI_SYMBOL_EXPORT int meshgen_loss_edge_checkout(void) {
  return 0;
}

CASADI_SYMBOL_EXPORT void meshgen_loss_edge_release(int mem) {
}

CASADI_SYMBOL_EXPORT void meshgen_loss_edge_incref(void) {
}

CASADI_SYMBOL_EXPORT void meshgen_loss_edge_decref(void) {
}

CASADI_SYMBOL_EXPORT casadi_int meshgen_loss_edge_n_in(void) { return 1;}

CASADI_SYMBOL_EXPORT casadi_int meshgen_loss_edge_n_out(void) { return 3;}

CASADI_SYMBOL_EXPORT casadi_real meshgen_loss_edge_default_in(casadi_int i) {
  switch (i) {
    default: return 0;
  }
}

CASADI_SYMBOL_EXPORT const char* meshgen_loss_edge_name_in(casadi_int i) {
  switch (i) {
    case 0: return "i0";
    default: return 0;
  }
}

CASADI_SYMBOL_EXPORT const char* meshgen_loss_edge_name_out(casadi_int i) {
  switch (i) {
    case 0: return "o0";
    case 1: return "o1";
    case 2: return "o2";
    default: return 0;
  }
}

CASADI_SYMBOL_EXPORT const casadi_int* meshgen_loss_edge_sparsity_in(casadi_int i) {
  switch (i) {
    case 0: return casadi_s0;
    default: return 0;
  }
}

CASADI_SYMBOL_EXPORT const casadi_int* meshgen_loss_edge_sparsity_out(casadi_int i) {
  switch (i) {
    case 0: return casadi_s1;
    case 1: return casadi_s2;
    case 2: return casadi_s1;
    default: return 0;
  }
}

CASADI_SYMBOL_EXPORT int meshgen_loss_edge_work(casadi_int *sz_arg, casadi_int* sz_res, casadi_int *sz_iw, casadi_int *sz_w) {
  if (sz_arg) *sz_arg = 1;
  if (sz_res) *sz_res = 3;
  if (sz_iw) *sz_iw = 0;
  if (sz_w) *sz_w = 0;
  return 0;
}


#ifdef __cplusplus
} /* extern "C" */
#endif
