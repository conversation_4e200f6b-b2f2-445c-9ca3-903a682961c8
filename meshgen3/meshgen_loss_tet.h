/* This file was automatically generated by CasADi 3.6.3.
 *  It consists of: 
 *   1) content generated by CasADi runtime: not copyrighted
 *   2) template code copied from CasADi source: permissively licensed (MIT-0)
 *   3) user code: owned by the user
 *
 */
#ifdef __cplusplus
extern "C" {
#endif

/* How to prefix internal symbols */
#ifdef CASADI_CODEGEN_PREFIX
  #define CASADI_NAMESPACE_CONCAT(NS, ID) _CASADI_NAMESPACE_CONCAT(NS, ID)
  #define _CASADI_NAMESPACE_CONCAT(NS, ID) NS ## ID
  #define CASADI_PREFIX(ID) CASADI_NAMESPACE_CONCAT(CODEGEN_PREFIX, ID)
#else
  #define CASADI_PREFIX(ID) meshgen_loss_tet_ ## ID
#endif

#include <math.h>

#ifndef casadi_real
#define casadi_real float
#endif

#ifndef casadi_int
#define casadi_int long long int
#endif

/* Add prefix to internal symbols */
#define casadi_f0 CASADI_PREFIX(f0)
#define casadi_s0 CASADI_PREFIX(s0)
#define casadi_s1 CASADI_PREFIX(s1)
#define casadi_s2 CASADI_PREFIX(s2)
#define casadi_sq CASADI_PREFIX(sq)

/* Symbol visibility in DLLs */
#ifndef CASADI_SYMBOL_EXPORT
  #if defined(_WIN32) || defined(__WIN32__) || defined(__CYGWIN__)
    #if defined(STATIC_LINKED)
      #define CASADI_SYMBOL_EXPORT
    #else
      #define CASADI_SYMBOL_EXPORT __declspec(dllexport)
    #endif
  #elif defined(__GNUC__) && defined(GCC_HASCLASSVISIBILITY)
    #define CASADI_SYMBOL_EXPORT __attribute__ ((visibility ("default")))
  #else
    #define CASADI_SYMBOL_EXPORT
  #endif
#endif

casadi_real casadi_sq(casadi_real x) { return x*x;}

static const casadi_int casadi_s0[16] = {12, 1, 0, 12, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11};
static const casadi_int casadi_s1[5] = {1, 1, 0, 1, 0};
static const casadi_int casadi_s2[27] = {1, 12, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0};

/* meshgen_loss_tet:(i0[12])->(o0,o1[1x12],o2) */
static int casadi_f0(const casadi_real** arg, casadi_real** res, casadi_int* iw, casadi_real* w, int mem) {
  casadi_real a0, a1, a10, a11, a12, a13, a14, a15, a16, a17, a18, a19, a2, a20, a21, a22, a23, a24, a25, a26, a27, a28, a29, a3, a30, a31, a32, a33, a34, a4, a5, a6, a7, a8, a9;
  a0=arg[0]? arg[0][3] : 0;
  a1=arg[0]? arg[0][0] : 0;
  a2=(a0-a1);
  a3=(a1+a0);
  a4=arg[0]? arg[0][6] : 0;
  a3=(a3+a4);
  a5=arg[0]? arg[0][9] : 0;
  a3=(a3+a5);
  a6=4.;
  a3=(a3/a6);
  a7=(a1-a3);
  a8=casadi_sq(a7);
  a9=arg[0]? arg[0][1] : 0;
  a10=arg[0]? arg[0][4] : 0;
  a11=(a9+a10);
  a12=arg[0]? arg[0][7] : 0;
  a11=(a11+a12);
  a13=arg[0]? arg[0][10] : 0;
  a11=(a11+a13);
  a11=(a11/a6);
  a14=(a9-a11);
  a15=casadi_sq(a14);
  a8=(a8+a15);
  a15=arg[0]? arg[0][2] : 0;
  a16=arg[0]? arg[0][5] : 0;
  a17=(a15+a16);
  a18=arg[0]? arg[0][8] : 0;
  a17=(a17+a18);
  a19=arg[0]? arg[0][11] : 0;
  a17=(a17+a19);
  a17=(a17/a6);
  a6=(a15-a17);
  a20=casadi_sq(a6);
  a8=(a8+a20);
  a0=(a0-a3);
  a20=casadi_sq(a0);
  a8=(a8+a20);
  a20=(a10-a11);
  a21=casadi_sq(a20);
  a8=(a8+a21);
  a21=(a16-a17);
  a22=casadi_sq(a21);
  a8=(a8+a22);
  a22=(a4-a3);
  a23=casadi_sq(a22);
  a8=(a8+a23);
  a23=(a12-a11);
  a24=casadi_sq(a23);
  a8=(a8+a24);
  a24=(a18-a17);
  a25=casadi_sq(a24);
  a8=(a8+a25);
  a3=(a5-a3);
  a25=casadi_sq(a3);
  a8=(a8+a25);
  a11=(a13-a11);
  a25=casadi_sq(a11);
  a8=(a8+a25);
  a17=(a19-a17);
  a25=casadi_sq(a17);
  a8=(a8+a25);
  a25=sqrt(a8);
  a2=(a2/a25);
  a12=(a12-a9);
  a12=(a12/a25);
  a19=(a19-a15);
  a19=(a19/a25);
  a26=(a12*a19);
  a13=(a13-a9);
  a13=(a13/a25);
  a18=(a18-a15);
  a18=(a18/a25);
  a27=(a13*a18);
  a26=(a26-a27);
  a27=(a2*a26);
  a4=(a4-a1);
  a4=(a4/a25);
  a10=(a10-a9);
  a10=(a10/a25);
  a9=(a10*a19);
  a16=(a16-a15);
  a16=(a16/a25);
  a15=(a13*a16);
  a9=(a9-a15);
  a15=(a4*a9);
  a27=(a27-a15);
  a5=(a5-a1);
  a5=(a5/a25);
  a1=(a10*a18);
  a15=(a12*a16);
  a1=(a1-a15);
  a15=(a5*a1);
  a27=(a27+a15);
  a15=log(a27);
  a15=(-a15);
  if (res[0]!=0) res[0][0]=a15;
  a1=(a1/a27);
  a15=(a1/a25);
  a9=(a9/a27);
  a28=(a9/a25);
  a29=(a15-a28);
  a7=(a7+a7);
  a30=(a5/a25);
  a30=(a30*a1);
  a1=(a16/a25);
  a5=(a5/a27);
  a31=(a12*a5);
  a32=(a4/a27);
  a33=(a13*a32);
  a31=(a31-a33);
  a1=(a1*a31);
  a30=(a30-a1);
  a1=(a10/a25);
  a33=(a19*a32);
  a34=(a18*a5);
  a33=(a33-a34);
  a1=(a1*a33);
  a30=(a30-a1);
  a4=(a4/a25);
  a4=(a4*a9);
  a30=(a30-a4);
  a4=(a18/a25);
  a9=(a2/a27);
  a1=(a13*a9);
  a34=(a10*a5);
  a1=(a1-a34);
  a4=(a4*a1);
  a30=(a30-a4);
  a13=(a13/a25);
  a18=(a18*a9);
  a4=(a16*a32);
  a18=(a18-a4);
  a13=(a13*a18);
  a30=(a30-a13);
  a13=(a19/a25);
  a10=(a10*a32);
  a32=(a12*a9);
  a10=(a10-a32);
  a13=(a13*a10);
  a30=(a30-a13);
  a12=(a12/a25);
  a16=(a16*a5);
  a19=(a19*a9);
  a16=(a16-a19);
  a12=(a12*a16);
  a30=(a30-a12);
  a2=(a2/a25);
  a26=(a26/a27);
  a2=(a2*a26);
  a30=(a30+a2);
  a2=(a25+a25);
  a30=(a30/a2);
  a7=(a7*a30);
  a29=(a29+a7);
  a2=2.5000000000000000e-01;
  a3=(a3+a3);
  a3=(a3*a30);
  a22=(a22+a22);
  a22=(a22*a30);
  a27=(a3+a22);
  a0=(a0+a0);
  a0=(a0*a30);
  a27=(a27+a0);
  a27=(a27+a7);
  a27=(a2*a27);
  a29=(a29-a27);
  a26=(a26/a25);
  a29=(a29+a26);
  if (res[1]!=0) res[1][0]=a29;
  a14=(a14+a14);
  a14=(a14*a30);
  a33=(a33/a25);
  a18=(a18/a25);
  a29=(a33+a18);
  a16=(a16/a25);
  a29=(a29+a16);
  a29=(a14-a29);
  a11=(a11+a11);
  a11=(a11*a30);
  a23=(a23+a23);
  a23=(a23*a30);
  a7=(a11+a23);
  a20=(a20+a20);
  a20=(a20*a30);
  a7=(a7+a20);
  a7=(a7+a14);
  a7=(a2*a7);
  a29=(a29-a7);
  if (res[1]!=0) res[1][1]=a29;
  a6=(a6+a6);
  a6=(a6*a30);
  a31=(a31/a25);
  a1=(a1/a25);
  a29=(a31+a1);
  a10=(a10/a25);
  a29=(a29+a10);
  a29=(a6-a29);
  a17=(a17+a17);
  a17=(a17*a30);
  a24=(a24+a24);
  a24=(a24*a30);
  a25=(a17+a24);
  a21=(a21+a21);
  a21=(a21*a30);
  a25=(a25+a21);
  a25=(a25+a6);
  a2=(a2*a25);
  a29=(a29-a2);
  if (res[1]!=0) res[1][2]=a29;
  a0=(a0-a27);
  a0=(a0-a26);
  if (res[1]!=0) res[1][3]=a0;
  a33=(a33+a20);
  a33=(a33-a7);
  if (res[1]!=0) res[1][4]=a33;
  a31=(a31+a21);
  a31=(a31-a2);
  if (res[1]!=0) res[1][5]=a31;
  a28=(a28+a22);
  a28=(a28-a27);
  if (res[1]!=0) res[1][6]=a28;
  a16=(a16+a23);
  a16=(a16-a7);
  if (res[1]!=0) res[1][7]=a16;
  a1=(a1+a24);
  a1=(a1-a2);
  if (res[1]!=0) res[1][8]=a1;
  a3=(a3-a15);
  a3=(a3-a27);
  if (res[1]!=0) res[1][9]=a3;
  a18=(a18+a11);
  a18=(a18-a7);
  if (res[1]!=0) res[1][10]=a18;
  a10=(a10+a17);
  a10=(a10-a2);
  if (res[1]!=0) res[1][11]=a10;
  if (res[2]!=0) res[2][0]=a8;
  return 0;
}

CASADI_SYMBOL_EXPORT int meshgen_loss_tet(const casadi_real** arg, casadi_real** res, casadi_int* iw, casadi_real* w, int mem){
  return casadi_f0(arg, res, iw, w, mem);
}

CASADI_SYMBOL_EXPORT int meshgen_loss_tet_alloc_mem(void) {
  return 0;
}

CASADI_SYMBOL_EXPORT int meshgen_loss_tet_init_mem(int mem) {
  return 0;
}

CASADI_SYMBOL_EXPORT void meshgen_loss_tet_free_mem(int mem) {
}

CASADI_SYMBOL_EXPORT int meshgen_loss_tet_checkout(void) {
  return 0;
}

CASADI_SYMBOL_EXPORT void meshgen_loss_tet_release(int mem) {
}

CASADI_SYMBOL_EXPORT void meshgen_loss_tet_incref(void) {
}

CASADI_SYMBOL_EXPORT void meshgen_loss_tet_decref(void) {
}

CASADI_SYMBOL_EXPORT casadi_int meshgen_loss_tet_n_in(void) { return 1;}

CASADI_SYMBOL_EXPORT casadi_int meshgen_loss_tet_n_out(void) { return 3;}

CASADI_SYMBOL_EXPORT casadi_real meshgen_loss_tet_default_in(casadi_int i) {
  switch (i) {
    default: return 0;
  }
}

CASADI_SYMBOL_EXPORT const char* meshgen_loss_tet_name_in(casadi_int i) {
  switch (i) {
    case 0: return "i0";
    default: return 0;
  }
}

CASADI_SYMBOL_EXPORT const char* meshgen_loss_tet_name_out(casadi_int i) {
  switch (i) {
    case 0: return "o0";
    case 1: return "o1";
    case 2: return "o2";
    default: return 0;
  }
}

CASADI_SYMBOL_EXPORT const casadi_int* meshgen_loss_tet_sparsity_in(casadi_int i) {
  switch (i) {
    case 0: return casadi_s0;
    default: return 0;
  }
}

CASADI_SYMBOL_EXPORT const casadi_int* meshgen_loss_tet_sparsity_out(casadi_int i) {
  switch (i) {
    case 0: return casadi_s1;
    case 1: return casadi_s2;
    case 2: return casadi_s1;
    default: return 0;
  }
}

CASADI_SYMBOL_EXPORT int meshgen_loss_tet_work(casadi_int *sz_arg, casadi_int* sz_res, casadi_int *sz_iw, casadi_int *sz_w) {
  if (sz_arg) *sz_arg = 1;
  if (sz_res) *sz_res = 3;
  if (sz_iw) *sz_iw = 0;
  if (sz_w) *sz_w = 0;
  return 0;
}


#ifdef __cplusplus
} /* extern "C" */
#endif
