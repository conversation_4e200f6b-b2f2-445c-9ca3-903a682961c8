var e;e||(e=typeof Module !== 'undefined' ? Module : {});var aa=Object.assign({},e),ba="./this.program",ca=(a,b)=>{throw b;},da="object"==typeof window,k="function"==typeof importScripts,ea="object"==typeof process&&"object"==typeof process.versions&&"string"==typeof process.versions.node,m="",fa,ha,ia;
if(ea){var fs=require("fs"),ja=require("path");m=k?ja.dirname(m)+"/":__dirname+"/";fa=(a,b)=>{a=a.startsWith("file://")?new URL(a):ja.normalize(a);return fs.readFileSync(a,b?void 0:"utf8")};ia=a=>{a=fa(a,!0);a.buffer||(a=new Uint8Array(a));return a};ha=(a,b,c,d=!0)=>{a=a.startsWith("file://")?new URL(a):ja.normalize(a);fs.readFile(a,d?void 0:"utf8",(f,g)=>{f?c(f):b(d?g.buffer:g)})};!e.thisProgram&&1<process.argv.length&&(ba=process.argv[1].replace(/\\/g,"/"));process.argv.slice(2);"undefined"!=typeof module&&
(module.exports=e);process.on("uncaughtException",a=>{if(!("unwind"===a||a instanceof ka||a.context instanceof ka))throw a;});if(15>process.versions.node.split(".")[0])process.on("unhandledRejection",a=>{throw a;});ca=(a,b)=>{process.exitCode=a;throw b;};e.inspect=()=>"[Emscripten Module object]"}else if(da||k)k?m=self.location.href:"undefined"!=typeof document&&document.currentScript&&(m=document.currentScript.src),m=0!==m.indexOf("blob:")?m.substr(0,m.replace(/[?#].*/,"").lastIndexOf("/")+1):"",
fa=a=>{var b=new XMLHttpRequest;b.open("GET",a,!1);b.send(null);return b.responseText},k&&(ia=a=>{var b=new XMLHttpRequest;b.open("GET",a,!1);b.responseType="arraybuffer";b.send(null);return new Uint8Array(b.response)}),ha=(a,b,c)=>{var d=new XMLHttpRequest;d.open("GET",a,!0);d.responseType="arraybuffer";d.onload=()=>{200==d.status||0==d.status&&d.response?b(d.response):c()};d.onerror=c;d.send(null)};var q=e.print||console.log.bind(console),t=e.printErr||console.error.bind(console);
Object.assign(e,aa);aa=null;e.thisProgram&&(ba=e.thisProgram);e.quit&&(ca=e.quit);var la;e.wasmBinary&&(la=e.wasmBinary);var noExitRuntime=e.noExitRuntime||!0;"object"!=typeof WebAssembly&&v("no native wasm support detected");var ma,A=!1,na,B,C,oa,pa,D,E,qa;
function ra(){var a=ma.buffer;e.HEAP8=B=new Int8Array(a);e.HEAP16=oa=new Int16Array(a);e.HEAP32=D=new Int32Array(a);e.HEAPU8=C=new Uint8Array(a);e.HEAPU16=pa=new Uint16Array(a);e.HEAPU32=E=new Uint32Array(a);e.HEAPF32=qa=new Float32Array(a);e.HEAPF64=new Float64Array(a)}var sa,ta=[],ua=[],va=[],wa=[];function xa(){var a=e.preRun.shift();ta.unshift(a)}var F=0,ya=null,za=null;
function v(a){if(e.onAbort)e.onAbort(a);a="Aborted("+a+")";t(a);A=!0;na=1;throw new WebAssembly.RuntimeError(a+". Build with -sASSERTIONS for more info.");}function Aa(a){return a.startsWith("data:application/octet-stream;base64,")}var G;G="module.wasm";if(!Aa(G)){var Ba=G;G=e.locateFile?e.locateFile(Ba,m):m+Ba}function Ca(a){try{if(a==G&&la)return new Uint8Array(la);if(ia)return ia(a);throw"both async and sync fetching of the wasm failed";}catch(b){v(b)}}
function Da(a){if(!la&&(da||k)){if("function"==typeof fetch&&!a.startsWith("file://"))return fetch(a,{credentials:"same-origin"}).then(b=>{if(!b.ok)throw"failed to load wasm binary file at '"+a+"'";return b.arrayBuffer()}).catch(()=>Ca(a));if(ha)return new Promise((b,c)=>{ha(a,d=>b(new Uint8Array(d)),c)})}return Promise.resolve().then(()=>Ca(a))}function Ea(a,b,c){return Da(a).then(d=>WebAssembly.instantiate(d,b)).then(d=>d).then(c,d=>{t("failed to asynchronously prepare wasm: "+d);v(d)})}
function Fa(a,b){var c=G;la||"function"!=typeof WebAssembly.instantiateStreaming||Aa(c)||c.startsWith("file://")||ea||"function"!=typeof fetch?Ea(c,a,b):fetch(c,{credentials:"same-origin"}).then(d=>WebAssembly.instantiateStreaming(d,a).then(b,function(f){t("wasm streaming compile failed: "+f);t("falling back to ArrayBuffer instantiation");return Ea(c,a,b)}))}function ka(a){this.name="ExitStatus";this.message="Program terminated with exit("+a+")";this.status=a}
function Ga(a){for(;0<a.length;)a.shift()(e)}var Ha="undefined"!=typeof TextDecoder?new TextDecoder("utf8"):void 0;
function H(a,b,c){var d=b+c;for(c=b;a[c]&&!(c>=d);)++c;if(16<c-b&&a.buffer&&Ha)return Ha.decode(a.subarray(b,c));for(d="";b<c;){var f=a[b++];if(f&128){var g=a[b++]&63;if(192==(f&224))d+=String.fromCharCode((f&31)<<6|g);else{var l=a[b++]&63;f=224==(f&240)?(f&15)<<12|g<<6|l:(f&7)<<18|g<<12|l<<6|a[b++]&63;65536>f?d+=String.fromCharCode(f):(f-=65536,d+=String.fromCharCode(55296|f>>10,56320|f&1023))}}else d+=String.fromCharCode(f)}return d}function I(a,b){return a?H(C,a,b):""}
function Ia(a){this.lb=a-24;this.Yb=function(b){E[this.lb+4>>2]=b};this.Xb=function(b){E[this.lb+8>>2]=b};this.Wa=function(b,c){this.Wb();this.Yb(b);this.Xb(c)};this.Wb=function(){E[this.lb+16>>2]=0}}var Ja=0,Ka=0,La;La=ea?()=>{var a=process.hrtime();return 1E3*a[0]+a[1]/1E6}:()=>performance.now();function Ma(a){a instanceof ka||"unwind"==a||ca(1,a)}function Na(a){na=na=a;if(!noExitRuntime){if(e.onExit)e.onExit(a);A=!0}ca(a,new ka(a))}
function Oa(a){if(!A)try{if(a(),!noExitRuntime)try{Na(na)}catch(b){Ma(b)}}catch(b){Ma(b)}}function Pa(a){setTimeout(()=>{Oa(a)},1E4)}var Qa=e.preloadPlugins||[],Ra=!1,J=null,K=0,Sa=null,Ta=0,Ua=0,Va=0,L=0,Wa=[],Xa={},Ya,Za,$a,ab=!1,bb=!1,cb=[];
function db(){function a(){bb=document.pointerLockElement===e.canvas||document.mozPointerLockElement===e.canvas||document.webkitPointerLockElement===e.canvas||document.msPointerLockElement===e.canvas}if(!eb){eb=!0;Qa.push({canHandle:function(c){return!e.Hc&&/\.(jpg|jpeg|png|bmp)$/i.test(c)},handle:function(c,d,f,g){var l=new Blob([c],{type:fb(d)});l.size!==c.length&&(l=new Blob([(new Uint8Array(c)).buffer],{type:fb(d)}));var n=URL.createObjectURL(l),r=new Image;r.onload=()=>{r.complete||v("Image "+
d+" could not be decoded");var p=document.createElement("canvas");p.width=r.width;p.height=r.height;p.getContext("2d").drawImage(r,0,0);URL.revokeObjectURL(n);f&&f(c)};r.onerror=()=>{q("Image "+n+" could not be decoded");g&&g()};r.src=n}});Qa.push({canHandle:function(c){return!e.Gc&&c.substr(-4)in{".ogg":1,".wav":1,".mp3":1}},handle:function(c,d,f){function g(){l||(l=!0,f&&f(c))}var l=!1,n=URL.createObjectURL(new Blob([c],{type:fb(d)})),r=new Audio;r.addEventListener("canplaythrough",()=>g(r),!1);
r.onerror=function(){if(!l){t("warning: browser could not fully decode audio "+d+", trying slower base64 approach");for(var p="",u=0,x=0,z=0;z<c.length;z++)for(u=u<<8|c[z],x+=8;6<=x;){var h=u>>x-6&63;x-=6;p+="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/"[h]}2==x?(p+="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/"[(u&3)<<4],p+="=="):4==x&&(p+="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/"[(u&15)<<2],p+="=");r.src="data:audio/x-"+d.substr(-3)+
";base64,"+p;g(r)}};r.src=n;Pa(()=>{g(r)})}});var b=e.canvas;b&&(b.requestPointerLock=b.requestPointerLock||b.mozRequestPointerLock||b.webkitRequestPointerLock||b.msRequestPointerLock||(()=>{}),b.exitPointerLock=document.exitPointerLock||document.mozExitPointerLock||document.webkitExitPointerLock||document.msExitPointerLock||(()=>{}),b.exitPointerLock=b.exitPointerLock.bind(document),document.addEventListener("pointerlockchange",a,!1),document.addEventListener("mozpointerlockchange",a,!1),document.addEventListener("webkitpointerlockchange",
a,!1),document.addEventListener("mspointerlockchange",a,!1),e.elementPointerLock&&b.addEventListener("click",c=>{!bb&&e.canvas.requestPointerLock&&(e.canvas.requestPointerLock(),c.preventDefault())},!1))}}
function gb(a,b,c,d){if(b&&e.cb&&a==e.canvas)return e.cb;var f;if(b){var g={antialias:!1,alpha:!1,fc:2};if(d)for(var l in d)g[l]=d[l];if("undefined"!=typeof hb&&(f=ib(a,g)))var n=jb[f].ob}else n=a.getContext("2d");if(!n)return null;c&&(b||"undefined"==typeof M||v("cannot set in module if GLctx is used, but we are a non-GL context that would replace it"),e.cb=n,b&&(kb=jb[f],e.cb=M=kb&&kb.ob),e.Kc=b,cb.forEach(r=>r()),db());return n}var lb=!1,mb=void 0,nb=void 0;
function ob(a,b){function c(){ab=!1;var g=d.parentNode;(document.fullscreenElement||document.mozFullScreenElement||document.msFullscreenElement||document.webkitFullscreenElement||document.webkitCurrentFullScreenElement)===g?(d.exitFullscreen=pb,mb&&d.requestPointerLock(),ab=!0,nb?("undefined"!=typeof SDL&&(D[SDL.screen>>2]=E[SDL.screen>>2]|8388608),qb(e.canvas),rb()):qb(d)):(g.parentNode.insertBefore(d,g),g.parentNode.removeChild(g),nb?("undefined"!=typeof SDL&&(D[SDL.screen>>2]=E[SDL.screen>>2]&
-8388609),qb(e.canvas),rb()):qb(d));if(e.onFullScreen)e.onFullScreen(ab);if(e.onFullscreen)e.onFullscreen(ab)}mb=a;nb=b;"undefined"==typeof mb&&(mb=!0);"undefined"==typeof nb&&(nb=!1);var d=e.canvas;lb||(lb=!0,document.addEventListener("fullscreenchange",c,!1),document.addEventListener("mozfullscreenchange",c,!1),document.addEventListener("webkitfullscreenchange",c,!1),document.addEventListener("MSFullscreenChange",c,!1));var f=document.createElement("div");d.parentNode.insertBefore(f,d);f.appendChild(d);
f.requestFullscreen=f.requestFullscreen||f.mozRequestFullScreen||f.msRequestFullscreen||(f.webkitRequestFullscreen?()=>f.webkitRequestFullscreen(Element.ALLOW_KEYBOARD_INPUT):null)||(f.webkitRequestFullScreen?()=>f.webkitRequestFullScreen(Element.ALLOW_KEYBOARD_INPUT):null);f.requestFullscreen()}
function pb(){if(!ab)return!1;(document.exitFullscreen||document.cancelFullScreen||document.mozCancelFullScreen||document.msExitFullscreen||document.webkitCancelFullScreen||(()=>{})).apply(document,[]);return!0}var sb=0;function tb(a){if("function"==typeof requestAnimationFrame)requestAnimationFrame(a);else{var b=Date.now();if(0===sb)sb=b+1E3/60;else for(;b+2>=sb;)sb+=1E3/60;setTimeout(a,Math.max(sb-b,0))}}
function fb(a){return{jpg:"image/jpeg",jpeg:"image/jpeg",png:"image/png",bmp:"image/bmp",ogg:"audio/ogg",wav:"audio/wav",mp3:"audio/mpeg"}[a.substr(a.lastIndexOf(".")+1)]}var ub=0,vb=0,wb=0,xb=0;
function yb(a){if(bb)"mousemove"!=a.type&&"mozMovementX"in a?wb=xb=0:(wb=a.movementX||a.mozMovementX||a.webkitMovementX||0,xb=a.movementY||a.mozMovementY||a.webkitMovementY||0),"undefined"!=typeof SDL?(ub=SDL.Dc+wb,vb=SDL.Ec+xb):(ub+=wb,vb+=xb);else{var b=e.canvas.getBoundingClientRect(),c=e.canvas.width,d=e.canvas.height,f="undefined"!=typeof window.scrollX?window.scrollX:window.pageXOffset,g="undefined"!=typeof window.scrollY?window.scrollY:window.pageYOffset;"touchstart"!==a.type&&"touchend"!==
a.type&&"touchmove"!==a.type&&(f=a.pageX-(f+b.left),a=a.pageY-(g+b.top),f*=c/b.width,a*=d/b.height,wb=f-ub,xb=a-vb,ub=f,vb=a)}}var zb=[];function rb(){var a=e.canvas;zb.forEach(b=>b(a.width,a.height))}function Ab(a,b,c){qb(e.canvas,a,b);c||rb()}
function qb(a,b,c){b&&c?(a.qc=b,a.dc=c):(b=a.qc,c=a.dc);var d=b,f=c;e.forcedAspectRatio&&0<e.forcedAspectRatio&&(d/f<e.forcedAspectRatio?d=Math.round(f*e.forcedAspectRatio):f=Math.round(d/e.forcedAspectRatio));if((document.fullscreenElement||document.mozFullScreenElement||document.msFullscreenElement||document.webkitFullscreenElement||document.webkitCurrentFullScreenElement)===a.parentNode&&"undefined"!=typeof screen){var g=Math.min(screen.width/d,screen.height/f);d=Math.round(d*g);f=Math.round(f*
g)}nb?(a.width!=d&&(a.width=d),a.height!=f&&(a.height=f),"undefined"!=typeof a.style&&(a.style.removeProperty("width"),a.style.removeProperty("height"))):(a.width!=b&&(a.width=b),a.height!=c&&(a.height=c),"undefined"!=typeof a.style&&(d!=b||f!=c?(a.style.setProperty("width",d+"px","important"),a.style.setProperty("height",f+"px","important")):(a.style.removeProperty("width"),a.style.removeProperty("height"))))}var eb;
function Bb(a,b){Ua=a;Va=b;if(Sa)if(Ra||(Ra=!0),0==a)J=function(){var d=Math.max(0,Za+b-La())|0;setTimeout($a,d)};else if(1==a)J=function(){tb($a)};else if(2==a){if("undefined"==typeof setImmediate){var c=[];addEventListener("message",d=>{if("setimmediate"===d.data||"setimmediate"===d.data.target)d.stopPropagation(),c.shift()()},!0);setImmediate=function(d){c.push(d);k?(void 0===e.setImmediates&&(e.setImmediates=[]),e.setImmediates.push(d),postMessage({target:"setimmediate"})):postMessage("setimmediate",
"*")}}J=function(){setImmediate($a)}}}
function Cb(a,b,c,d,f){!Sa||v("emscripten_set_main_loop: there can only be one main loop function at once: call emscripten_cancel_main_loop to cancel the previous one before setting a new one with different parameters.");Sa=a;Ta=d;var g=K;Ra=!1;$a=function(){if(!A)if(0<Wa.length){var l=Date.now(),n=Wa.shift();n.zc(n.tc);if(Ya){var r=Ya,p=0==r%1?r-1:Math.floor(r);Ya=n.wc?p:(8*r+(p+.5))/9}q('main loop blocker "'+n.name+'" took '+(Date.now()-l)+" ms");e.setStatus&&(l=e.statusMessage||"Please wait...",
n=Ya,r=Xa.yc,n?n<r?e.setStatus(l+" ("+(r-n)+"/"+r+")"):e.setStatus(l):e.setStatus(""));g<K||setTimeout($a,0)}else g<K||(L=L+1|0,1==Ua&&1<Va&&0!=L%Va?J():(0==Ua&&(Za=La()),A||e.preMainLoop&&!1===e.preMainLoop()||(Oa(a),e.postMainLoop&&e.postMainLoop()),g<K||("object"==typeof SDL&&SDL.audio&&SDL.audio.jc&&SDL.audio.jc(),J())))};f||(b&&0<b?Bb(0,1E3/b):Bb(1,1),J());if(c)throw"unwind";}var Db=[];function N(a){var b=Db[a];b||(a>=Db.length&&(Db.length=a+1),Db[a]=b=sa.get(a));return b}var Eb={};
function Fb(){if(!Gb){var a={USER:"web_user",LOGNAME:"web_user",PATH:"/",PWD:"/",HOME:"/home/<USER>",LANG:("object"==typeof navigator&&navigator.languages&&navigator.languages[0]||"C").replace("-","_")+".UTF-8",_:ba||"./this.program"},b;for(b in Eb)void 0===Eb[b]?delete a[b]:a[b]=Eb[b];var c=[];for(b in a)c.push(b+"="+a[b]);Gb=c}return Gb}var Gb,Hb=[null,[],[]],Ib=1,Jb=[],O=[],Kb=[],Lb=[],P=[],Mb=[],jb=[];function Q(a){Nb||(Nb=a)}
function Ob(a){for(var b=Ib++,c=a.length;c<b;c++)a[c]=null;return b}function ib(a,b){a.Wa||(a.Wa=a.getContext,a.getContext=function(d,f){f=a.Wa(d,f);return"webgl"==d==f instanceof WebGLRenderingContext?f:null});var c=a.getContext("webgl2",b);return c?Pb(c,b):0}function Pb(a,b){var c=Ob(jb),d={Bc:c,attributes:b,version:b.fc,ob:a};a.canvas&&(a.canvas.rc=d);jb[c]=d;("undefined"==typeof b.bc||b.bc)&&Qb(d);return c}
function Qb(a){a||(a=kb);if(!a.ec){a.ec=!0;var b=a.ob;b.xc=b.getExtension("WEBGL_draw_instanced_base_vertex_base_instance");b.Cc=b.getExtension("WEBGL_multi_draw_instanced_base_vertex_base_instance");2<=a.version&&(b.Cb=b.getExtension("EXT_disjoint_timer_query_webgl2"));if(2>a.version||!b.Cb)b.Cb=b.getExtension("EXT_disjoint_timer_query");b.Fc=b.getExtension("WEBGL_multi_draw");(b.getSupportedExtensions()||[]).forEach(function(c){c.includes("lose_context")||c.includes("debug")||b.getExtension(c)})}}
var hb={},Nb,kb;function Rb(a,b,c,d){for(var f=0;f<a;f++){var g=M[c](),l=g&&Ob(d);g?(g.name=l,d[l]=g):Q(1282);D[b+4*f>>2]=l}}
function Sb(a,b,c,d){if(!(0<d))return 0;var f=c;d=c+d-1;for(var g=0;g<a.length;++g){var l=a.charCodeAt(g);if(55296<=l&&57343>=l){var n=a.charCodeAt(++g);l=65536+((l&1023)<<10)|n&1023}if(127>=l){if(c>=d)break;b[c++]=l}else{if(2047>=l){if(c+1>=d)break;b[c++]=192|l>>6}else{if(65535>=l){if(c+2>=d)break;b[c++]=224|l>>12}else{if(c+3>=d)break;b[c++]=240|l>>18;b[c++]=128|l>>12&63}b[c++]=128|l>>6&63}b[c++]=128|l&63}}b[c]=0;return c-f}function Tb(a){return"]"==a.slice(-1)&&a.lastIndexOf("[")}
function Ub(a){a-=5120;return 0==a?B:1==a?C:2==a?oa:4==a?D:6==a?qa:5==a||28922==a||28520==a||30779==a||30782==a?E:pa}function Vb(a){var b=M.ac;if(b){var c=b.eb[a];"number"==typeof c&&(b.eb[a]=c=M.getUniformLocation(b,b.Sb[a]+(0<c?"["+c+"]":"")));return c}Q(1282)}
function Wb(a,b,c,d){this.id=a;this.y=this.x=0;this.ub=!1;this.Rb=this.Qb=0;this.width=b;this.height=c;this.Pb=b;this.Ob=c;this.title=d;this.attributes=R;this.buttons=0;this.keys=[];this.sb=[];this.Ab=this.Ib=this.tb=this.Nb=this.fb=this.rb=this.Lb=this.Ub=this.Gb=this.Vb=this.title=null}function Xb(a){for(var b=0,c=0;c<a.length;++c){var d=a.charCodeAt(c);127>=d?b++:2047>=d?b+=2:55296<=d&&57343>=d?(b+=4,++c):b+=3}return b}function Yb(a){var b=Xb(a)+1,c=Zb(b);c&&Sb(a,C,c,b);return c}
function $b(){if("object"==typeof crypto&&"function"==typeof crypto.getRandomValues)return c=>crypto.getRandomValues(c);if(ea)try{var a=require("crypto");if(a.randomFillSync)return c=>a.randomFillSync(c);var b=a.randomBytes;return c=>(c.set(b(c.byteLength)),c)}catch(c){}v("initRandomDevice")}function ac(a){return(ac=$b())(a)}
var bc=(a,b)=>{for(var c=0,d=a.length-1;0<=d;d--){var f=a[d];"."===f?a.splice(d,1):".."===f?(a.splice(d,1),c++):c&&(a.splice(d,1),c--)}if(b)for(;c;c--)a.unshift("..");return a},cc=a=>{var b="/"===a.charAt(0),c="/"===a.substr(-1);(a=bc(a.split("/").filter(d=>!!d),!b).join("/"))||b||(a=".");a&&c&&(a+="/");return(b?"/":"")+a},dc=a=>{var b=/^(\/?|)([\s\S]*?)((?:\.{1,2}|[^\/]+?|)(\.[^.\/]*|))(?:[\/]*)$/.exec(a).slice(1);a=b[0];b=b[1];if(!a&&!b)return".";b&&(b=b.substr(0,b.length-1));return a+b},ec=a=>
{if("/"===a)return"/";a=cc(a);a=a.replace(/\/$/,"");var b=a.lastIndexOf("/");return-1===b?a:a.substr(b+1)};function fc(){for(var a="",b=!1,c=arguments.length-1;-1<=c&&!b;c--){b=0<=c?arguments[c]:"/";if("string"!=typeof b)throw new TypeError("Arguments to path.resolve must be strings");if(!b)return"";a=b+"/"+a;b="/"===b.charAt(0)}a=bc(a.split("/").filter(d=>!!d),!b).join("/");return(b?"/":"")+a||"."}function gc(a,b){var c=Array(Xb(a)+1);a=Sb(a,c,0,c.length);b&&(c.length=a);return c}var hc=[];
function ic(a,b){hc[a]={input:[],output:[],bb:b};jc(a,kc)}
var kc={open:function(a){var b=hc[a.node.rdev];if(!b)throw new S(43);a.tty=b;a.seekable=!1},close:function(a){a.tty.bb.fsync(a.tty)},fsync:function(a){a.tty.bb.fsync(a.tty)},read:function(a,b,c,d){if(!a.tty||!a.tty.bb.Hb)throw new S(60);for(var f=0,g=0;g<d;g++){try{var l=a.tty.bb.Hb(a.tty)}catch(n){throw new S(29);}if(void 0===l&&0===f)throw new S(6);if(null===l||void 0===l)break;f++;b[c+g]=l}f&&(a.node.timestamp=Date.now());return f},write:function(a,b,c,d){if(!a.tty||!a.tty.bb.vb)throw new S(60);
try{for(var f=0;f<d;f++)a.tty.bb.vb(a.tty,b[c+f])}catch(g){throw new S(29);}d&&(a.node.timestamp=Date.now());return f}},lc={Hb:function(a){if(!a.input.length){var b=null;if(ea){var c=Buffer.alloc(256),d=0;try{d=fs.readSync(process.stdin.fd,c,0,256,-1)}catch(f){if(f.toString().includes("EOF"))d=0;else throw f;}0<d?b=c.slice(0,d).toString("utf-8"):b=null}else"undefined"!=typeof window&&"function"==typeof window.prompt?(b=window.prompt("Input: "),null!==b&&(b+="\n")):"function"==typeof readline&&(b=
readline(),null!==b&&(b+="\n"));if(!b)return null;a.input=gc(b,!0)}return a.input.shift()},vb:function(a,b){null===b||10===b?(q(H(a.output,0)),a.output=[]):0!=b&&a.output.push(b)},fsync:function(a){a.output&&0<a.output.length&&(q(H(a.output,0)),a.output=[])}},mc={vb:function(a,b){null===b||10===b?(t(H(a.output,0)),a.output=[]):0!=b&&a.output.push(b)},fsync:function(a){a.output&&0<a.output.length&&(t(H(a.output,0)),a.output=[])}},U={Ta:null,Xa:function(){return U.createNode(null,"/",16895,0)},createNode:function(a,
b,c,d){if(24576===(c&61440)||4096===(c&61440))throw new S(63);U.Ta||(U.Ta={dir:{node:{Ya:U.Qa.Ya,Ua:U.Qa.Ua,lookup:U.Qa.lookup,jb:U.Qa.jb,rename:U.Qa.rename,unlink:U.Qa.unlink,rmdir:U.Qa.rmdir,readdir:U.Qa.readdir,symlink:U.Qa.symlink},stream:{Za:U.Ra.Za}},file:{node:{Ya:U.Qa.Ya,Ua:U.Qa.Ua},stream:{Za:U.Ra.Za,read:U.Ra.read,write:U.Ra.write,zb:U.Ra.zb,Jb:U.Ra.Jb,Mb:U.Ra.Mb}},link:{node:{Ya:U.Qa.Ya,Ua:U.Qa.Ua,readlink:U.Qa.readlink},stream:{}},Bb:{node:{Ya:U.Qa.Ya,Ua:U.Qa.Ua},stream:nc}});c=oc(a,b,
c,d);16384===(c.mode&61440)?(c.Qa=U.Ta.dir.node,c.Ra=U.Ta.dir.stream,c.Pa={}):32768===(c.mode&61440)?(c.Qa=U.Ta.file.node,c.Ra=U.Ta.file.stream,c.Sa=0,c.Pa=null):40960===(c.mode&61440)?(c.Qa=U.Ta.link.node,c.Ra=U.Ta.link.stream):8192===(c.mode&61440)&&(c.Qa=U.Ta.Bb.node,c.Ra=U.Ta.Bb.stream);c.timestamp=Date.now();a&&(a.Pa[b]=c,a.timestamp=c.timestamp);return c},Ac:function(a){return a.Pa?a.Pa.subarray?a.Pa.subarray(0,a.Sa):new Uint8Array(a.Pa):new Uint8Array(0)},Db:function(a,b){var c=a.Pa?a.Pa.length:
0;c>=b||(b=Math.max(b,c*(1048576>c?2:1.125)>>>0),0!=c&&(b=Math.max(b,256)),c=a.Pa,a.Pa=new Uint8Array(b),0<a.Sa&&a.Pa.set(c.subarray(0,a.Sa),0))},kc:function(a,b){if(a.Sa!=b)if(0==b)a.Pa=null,a.Sa=0;else{var c=a.Pa;a.Pa=new Uint8Array(b);c&&a.Pa.set(c.subarray(0,Math.min(b,a.Sa)));a.Sa=b}},Qa:{Ya:function(a){var b={};b.dev=8192===(a.mode&61440)?a.id:1;b.ino=a.id;b.mode=a.mode;b.nlink=1;b.uid=0;b.gid=0;b.rdev=a.rdev;16384===(a.mode&61440)?b.size=4096:32768===(a.mode&61440)?b.size=a.Sa:40960===(a.mode&
61440)?b.size=a.link.length:b.size=0;b.atime=new Date(a.timestamp);b.mtime=new Date(a.timestamp);b.ctime=new Date(a.timestamp);b.Zb=4096;b.blocks=Math.ceil(b.size/b.Zb);return b},Ua:function(a,b){void 0!==b.mode&&(a.mode=b.mode);void 0!==b.timestamp&&(a.timestamp=b.timestamp);void 0!==b.size&&U.kc(a,b.size)},lookup:function(){throw pc[44];},jb:function(a,b,c,d){return U.createNode(a,b,c,d)},rename:function(a,b,c){if(16384===(a.mode&61440)){try{var d=qc(b,c)}catch(g){}if(d)for(var f in d.Pa)throw new S(55);
}delete a.parent.Pa[a.name];a.parent.timestamp=Date.now();a.name=c;b.Pa[c]=a;b.timestamp=a.parent.timestamp;a.parent=b},unlink:function(a,b){delete a.Pa[b];a.timestamp=Date.now()},rmdir:function(a,b){var c=qc(a,b),d;for(d in c.Pa)throw new S(55);delete a.Pa[b];a.timestamp=Date.now()},readdir:function(a){var b=[".",".."],c;for(c in a.Pa)a.Pa.hasOwnProperty(c)&&b.push(c);return b},symlink:function(a,b,c){a=U.createNode(a,b,41471,0);a.link=c;return a},readlink:function(a){if(40960!==(a.mode&61440))throw new S(28);
return a.link}},Ra:{read:function(a,b,c,d,f){var g=a.node.Pa;if(f>=a.node.Sa)return 0;a=Math.min(a.node.Sa-f,d);if(8<a&&g.subarray)b.set(g.subarray(f,f+a),c);else for(d=0;d<a;d++)b[c+d]=g[f+d];return a},write:function(a,b,c,d,f,g){b.buffer===B.buffer&&(g=!1);if(!d)return 0;a=a.node;a.timestamp=Date.now();if(b.subarray&&(!a.Pa||a.Pa.subarray)){if(g)return a.Pa=b.subarray(c,c+d),a.Sa=d;if(0===a.Sa&&0===f)return a.Pa=b.slice(c,c+d),a.Sa=d;if(f+d<=a.Sa)return a.Pa.set(b.subarray(c,c+d),f),d}U.Db(a,f+
d);if(a.Pa.subarray&&b.subarray)a.Pa.set(b.subarray(c,c+d),f);else for(g=0;g<d;g++)a.Pa[f+g]=b[c+g];a.Sa=Math.max(a.Sa,f+d);return d},Za:function(a,b,c){1===c?b+=a.position:2===c&&32768===(a.node.mode&61440)&&(b+=a.node.Sa);if(0>b)throw new S(28);return b},zb:function(a,b,c){U.Db(a.node,b+c);a.node.Sa=Math.max(a.node.Sa,b+c)},Jb:function(a,b,c,d,f){if(32768!==(a.node.mode&61440))throw new S(43);a=a.node.Pa;if(f&2||a.buffer!==B.buffer){if(0<c||c+b<a.length)a.subarray?a=a.subarray(c,c+b):a=Array.prototype.slice.call(a,
c,c+b);c=!0;v();b=void 0;if(!b)throw new S(48);B.set(a,b)}else c=!1,b=a.byteOffset;return{lb:b,sc:c}},Mb:function(a,b,c,d){U.Ra.write(a,b,0,d,c,!1);return 0}}};function rc(a,b){var c=0;a&&(c|=365);b&&(c|=146);return c}
var sc=null,tc={},uc=[],vc=1,wc=null,xc=!0,S=null,pc={},V=(a,b={})=>{a=fc(a);if(!a)return{path:"",node:null};b=Object.assign({Fb:!0,wb:0},b);if(8<b.wb)throw new S(32);a=a.split("/").filter(l=>!!l);for(var c=sc,d="/",f=0;f<a.length;f++){var g=f===a.length-1;if(g&&b.parent)break;c=qc(c,a[f]);d=cc(d+"/"+a[f]);c.kb&&(!g||g&&b.Fb)&&(c=c.kb.root);if(!g||b.Eb)for(g=0;40960===(c.mode&61440);)if(c=yc(d),d=fc(dc(d),c),c=V(d,{wb:b.wb+1}).node,40<g++)throw new S(32);}return{path:d,node:c}},zc=a=>{for(var b;;){if(a===
a.parent)return a=a.Xa.Kb,b?"/"!==a[a.length-1]?`${a}/${b}`:a+b:a;b=b?`${a.name}/${b}`:a.name;a=a.parent}},Ac=(a,b)=>{for(var c=0,d=0;d<b.length;d++)c=(c<<5)-c+b.charCodeAt(d)|0;return(a+c>>>0)%wc.length},qc=(a,b)=>{var c;if(c=(c=Bc(a,"x"))?c:a.Qa.lookup?0:2)throw new S(c,a);for(c=wc[Ac(a.id,b)];c;c=c.ic){var d=c.name;if(c.parent.id===a.id&&d===b)return c}return a.Qa.lookup(a,b)},oc=(a,b,c,d)=>{a=new Cc(a,b,c,d);b=Ac(a.parent.id,a.name);a.ic=wc[b];return wc[b]=a},Dc=a=>{var b=["r","w","rw"][a&3];
a&512&&(b+="w");return b},Bc=(a,b)=>{if(xc)return 0;if(!b.includes("r")||a.mode&292){if(b.includes("w")&&!(a.mode&146)||b.includes("x")&&!(a.mode&73))return 2}else return 2;return 0},Ec=(a,b)=>{try{return qc(a,b),20}catch(c){}return Bc(a,"wx")},Fc=()=>{for(var a=0;4096>=a;a++)if(!uc[a])return a;throw new S(33);},Hc=a=>{Gc||(Gc=function(){this.Wa={}},Gc.prototype={},Object.defineProperties(Gc.prototype,{object:{get:function(){return this.node},set:function(c){this.node=c}},flags:{get:function(){return this.Wa.flags},
set:function(c){this.Wa.flags=c}},position:{get:function(){return this.Wa.position},set:function(c){this.Wa.position=c}}}));a=Object.assign(new Gc,a);var b=Fc();a.fd=b;return uc[b]=a},nc={open:a=>{a.Ra=tc[a.node.rdev].Ra;a.Ra.open&&a.Ra.open(a)},Za:()=>{throw new S(70);}},jc=(a,b)=>{tc[a]={Ra:b}},Ic=(a,b)=>{var c="/"===b,d=!b;if(c&&sc)throw new S(10);if(!c&&!d){var f=V(b,{Fb:!1});b=f.path;f=f.node;if(f.kb)throw new S(10);if(16384!==(f.mode&61440))throw new S(54);}b={type:a,Ic:{},Kb:b,hc:[]};a=a.Xa(b);
a.Xa=b;b.root=a;c?sc=a:f&&(f.kb=b,f.Xa&&f.Xa.hc.push(b))},W=(a,b,c)=>{var d=V(a,{parent:!0}).node;a=ec(a);if(!a||"."===a||".."===a)throw new S(28);var f=Ec(d,a);if(f)throw new S(f);if(!d.Qa.jb)throw new S(63);return d.Qa.jb(d,a,b,c)},Jc=(a,b,c)=>{"undefined"==typeof c&&(c=b,b=438);W(a,b|8192,c)},Kc=(a,b)=>{if(!fc(a))throw new S(44);var c=V(b,{parent:!0}).node;if(!c)throw new S(44);b=ec(b);var d=Ec(c,b);if(d)throw new S(d);if(!c.Qa.symlink)throw new S(63);c.Qa.symlink(c,b,a)},yc=a=>{a=V(a).node;if(!a)throw new S(44);
if(!a.Qa.readlink)throw new S(28);return fc(zc(a.parent),a.Qa.readlink(a))},Mc=(a,b,c)=>{if(""===a)throw new S(44);if("string"==typeof b){var d={r:0,"r+":2,w:577,"w+":578,a:1089,"a+":1090}[b];if("undefined"==typeof d)throw Error(`Unknown file open mode: ${b}`);b=d}c=b&64?("undefined"==typeof c?438:c)&4095|32768:0;if("object"==typeof a)var f=a;else{a=cc(a);try{f=V(a,{Eb:!(b&131072)}).node}catch(g){}}d=!1;if(b&64)if(f){if(b&128)throw new S(20);}else f=W(a,c,0),d=!0;if(!f)throw new S(44);8192===(f.mode&
61440)&&(b&=-513);if(b&65536&&16384!==(f.mode&61440))throw new S(54);if(!d&&(c=f?40960===(f.mode&61440)?32:16384===(f.mode&61440)&&("r"!==Dc(b)||b&512)?31:Bc(f,Dc(b)):44))throw new S(c);if(b&512&&!d){c=f;c="string"==typeof c?V(c,{Eb:!0}).node:c;if(!c.Qa.Ua)throw new S(63);if(16384===(c.mode&61440))throw new S(31);if(32768!==(c.mode&61440))throw new S(28);if(d=Bc(c,"w"))throw new S(d);c.Qa.Ua(c,{size:0,timestamp:Date.now()})}b&=-131713;f=Hc({node:f,path:zc(f),flags:b,seekable:!0,position:0,Ra:f.Ra,
pc:[],error:!1});f.Ra.open&&f.Ra.open(f);!e.logReadFiles||b&1||(Lc||(Lc={}),a in Lc||(Lc[a]=1));return f},Nc=(a,b,c,d)=>{var f=void 0;if(0>c||0>f)throw new S(28);if(null===a.fd)throw new S(8);if(0===(a.flags&2097155))throw new S(8);if(16384===(a.node.mode&61440))throw new S(31);if(!a.Ra.write)throw new S(28);if(a.seekable&&a.flags&1024){if(null===a.fd)throw new S(8);if(!a.seekable||!a.Ra.Za)throw new S(70);a.position=a.Ra.Za(a,0,2);a.pc=[]}var g="undefined"!=typeof f;if(!g)f=a.position;else if(!a.seekable)throw new S(70);
b=a.Ra.write(a,b,0,c,f,d);g||(a.position+=b)},Oc=()=>{S||(S=function(a,b){this.name="ErrnoError";this.node=b;this.message="FS error"},S.prototype=Error(),S.prototype.constructor=S,[44].forEach(a=>{pc[a]=new S(a);pc[a].stack="<generic error, no stack>"}))},Pc,Qc=()=>{var a="/";a="string"==typeof a?a:zc(a);for(var b=[".glfw_dropped_files"].reverse();b.length;){var c=b.pop();if(c){a=cc(a+"/"+c);try{W(a,16895,0)}catch(d){}}}},Tc=(a,b,c)=>{a=cc("/dev/"+a);var d=rc(!!b,!!c);Rc||(Rc=64);var f=Rc++<<8|0;
jc(f,{open:g=>{g.seekable=!1},close:()=>{c&&c.buffer&&c.buffer.length&&c(10)},read:(g,l,n,r)=>{for(var p=0,u=0;u<r;u++){try{var x=b()}catch(z){throw new S(29);}if(void 0===x&&0===p)throw new S(6);if(null===x||void 0===x)break;p++;l[n+u]=x}p&&(g.node.timestamp=Date.now());return p},write:(g,l,n,r)=>{for(var p=0;p<r;p++)try{c(l[n+p])}catch(u){throw new S(29);}r&&(g.node.timestamp=Date.now());return p}});Jc(a,d,f)},Rc,Gc,Lc,X=null,Uc=null,Y=null,R=null,Vc={131073:0,131074:0,131075:1,131076:1,131077:1,
131082:0,135169:8,135170:8,135171:8,135172:8,135173:24,135174:8,135175:0,135176:0,135177:0,135178:0,135179:0,135180:0,135181:0,135182:0,135183:0,139265:196609,139266:1,139267:0,139268:0,139269:0,139270:0,139271:0,139272:0,139276:0};
function Wc(a){switch(a){case 32:return 32;case 222:return 39;case 188:return 44;case 173:return 45;case 189:return 45;case 190:return 46;case 191:return 47;case 48:return 48;case 49:return 49;case 50:return 50;case 51:return 51;case 52:return 52;case 53:return 53;case 54:return 54;case 55:return 55;case 56:return 56;case 57:return 57;case 59:return 59;case 61:return 61;case 187:return 61;case 65:return 65;case 66:return 66;case 67:return 67;case 68:return 68;case 69:return 69;case 70:return 70;case 71:return 71;
case 72:return 72;case 73:return 73;case 74:return 74;case 75:return 75;case 76:return 76;case 77:return 77;case 78:return 78;case 79:return 79;case 80:return 80;case 81:return 81;case 82:return 82;case 83:return 83;case 84:return 84;case 85:return 85;case 86:return 86;case 87:return 87;case 88:return 88;case 89:return 89;case 90:return 90;case 219:return 91;case 220:return 92;case 221:return 93;case 192:return 96;case 27:return 256;case 13:return 257;case 9:return 258;case 8:return 259;case 45:return 260;
case 46:return 261;case 39:return 262;case 37:return 263;case 40:return 264;case 38:return 265;case 33:return 266;case 34:return 267;case 36:return 268;case 35:return 269;case 20:return 280;case 145:return 281;case 144:return 282;case 44:return 283;case 19:return 284;case 112:return 290;case 113:return 291;case 114:return 292;case 115:return 293;case 116:return 294;case 117:return 295;case 118:return 296;case 119:return 297;case 120:return 298;case 121:return 299;case 122:return 300;case 123:return 301;
case 124:return 302;case 125:return 303;case 126:return 304;case 127:return 305;case 128:return 306;case 129:return 307;case 130:return 308;case 131:return 309;case 132:return 310;case 133:return 311;case 134:return 312;case 135:return 313;case 136:return 314;case 96:return 320;case 97:return 321;case 98:return 322;case 99:return 323;case 100:return 324;case 101:return 325;case 102:return 326;case 103:return 327;case 104:return 328;case 105:return 329;case 110:return 330;case 111:return 331;case 106:return 332;
case 109:return 333;case 107:return 334;case 16:return 340;case 17:return 341;case 18:return 342;case 91:return 343;case 93:return 348;default:return-1}}function Xc(){var a=X,b=0;a.keys[340]&&(b|=1);a.keys[341]&&(b|=2);a.keys[342]&&(b|=4);a.keys[343]&&(b|=8);return b}function Yc(a){X&&X.Ab&&!a.ctrlKey&&!a.metaKey&&(a=a.charCode,0==a||0<=a&&31>=a||N(X.Ab)(X.id,a))}function Zc(a,b){if(X){var c=Wc(a);if(-1!=c){var d=b&&X.keys[c];X.keys[c]=b;X.sb[a]=b;X.Ib&&(d&&(b=2),N(X.Ib)(X.id,c,a,b,Xc()))}}}
function $c(){ad()}function bd(){ad()}function cd(a){Zc(a.keyCode,1);8!==a.keyCode&&9!==a.keyCode||a.preventDefault()}function dd(a){Zc(a.keyCode,0)}function ed(){if(X)for(var a=0;a<X.sb.length;++a)X.sb[a]&&Zc(a,0)}function fd(a){X&&(yb(a),a.target==e.canvas&&X.rb&&X.rb&&N(X.rb)(X.id,ub,vb))}function gd(a){X&&a.target==e.canvas&&X.fb&&N(X.fb)(X.id,1)}function hd(a){X&&a.target==e.canvas&&X.fb&&N(X.fb)(X.id,0)}
function jd(a,b){if(X&&(yb(a),a.target==e.canvas)){var c=a.button;0<c&&(c=1==c?2:1);if(1==b){X.buttons|=1<<c;try{a.target.setCapture()}catch(d){}}else X.buttons&=~(1<<c);X.Lb&&N(X.Lb)(X.id,c,b,Xc())}}function kd(a){X&&jd(a,1)}function ld(a){X&&jd(a,0)}
function md(a){switch(a.type){case "DOMMouseScroll":var b=a.detail/3;break;case "mousewheel":b=a.wheelDelta/120;break;case "wheel":b=a.deltaY;switch(a.deltaMode){case 0:b/=100;break;case 1:b/=3;break;case 2:b*=80;break;default:throw"unrecognized mouse wheel delta mode: "+a.deltaMode;}break;default:throw"unrecognized mouse wheel event: "+a.type;}b=-b;b=0==b?0:0<b?Math.max(b,1):Math.min(b,-1);if(X&&X.Nb&&a.target==e.canvas){var c=b;b="mousewheel"==a.type?a.wheelDeltaX:a.deltaX;N(X.Nb)(X.id,b,c);a.preventDefault()}}
var Z={},nd=[],od=null;
function ad(){if(L!==od||!L){nd=navigator.getGamepads?navigator.getGamepads():navigator.webkitGetGamepads?navigator.webkitGetGamepads:[];od=L;for(var a=0;a<nd.length;++a){var b=nd[a];if(b){Z[a]||(q("glfw joystick connected:",a),Z[a]={id:Yb(b.id),vc:b.buttons.length,uc:b.axes.length,buttons:Zb(b.buttons.length),axes:Zb(4*b.axes.length)});for(var c=Z[a],d=0;d<b.buttons.length;++d)B[c.buttons+d>>0]=b.buttons[d].pressed;for(d=0;d<b.axes.length;++d)qa[c.axes+4*d>>2]=b.axes[d]}else Z[a]&&(q("glfw joystick disconnected",
a),pd(Z[a].id),pd(Z[a].buttons),pd(Z[a].axes),delete Z[a])}}}
function qd(a){function b(n){var r="/.glfw_dropped_files/"+n.name.replace(/\//g,"_"),p=new FileReader;p.onloadend=x=>{if(2!=p.readyState)++g,q("failed to read dropped file: "+n.name+": "+p.error);else{var z=new Uint8Array(x.target.result);x={};x.flags=x.flags||577;var h=Mc(r,x.flags,x.mode);if("string"==typeof z){var w=new Uint8Array(Xb(z)+1);z=Sb(z,w,0,w.length);Nc(h,w,z,x.$b)}else if(ArrayBuffer.isView(z))Nc(h,z,z.byteLength,x.$b);else throw Error("Unsupported data type");if(null===h.fd)throw new S(8);
h.cc&&(h.cc=null);try{h.Ra.close&&h.Ra.close(h)}catch(y){throw y;}finally{uc[h.fd]=null}h.fd=null;if(++g===f){N(X.tb)(X.id,f,c);for(x=0;x<d.length;++x)pd(d[x]);pd(c)}}};p.readAsArrayBuffer(n);var u=Yb(r);d.push(u);E[c+4*l>>2]=u}if(X&&X.tb&&a.dataTransfer&&a.dataTransfer.files&&0!=a.dataTransfer.files.length){a.preventDefault();var c=Zb(4*a.dataTransfer.files.length),d=[],f=a.dataTransfer.files.length,g=0;Qc();for(var l=0;l<f;++l)b(a.dataTransfer.files[l]);return!1}}
function rd(a){if(X&&X.tb)return a.preventDefault(),!1}function sd(a){return 0===a%4&&(0!==a%100||0===a%400)}var td=[31,29,31,30,31,30,31,31,30,31,30,31],ud=[31,28,31,30,31,30,31,31,30,31,30,31];
function vd(a,b,c,d){function f(h,w,y){for(h="number"==typeof h?h.toString():h||"";h.length<w;)h=y[0]+h;return h}function g(h,w){return f(h,w,"0")}function l(h,w){function y(Sc){return 0>Sc?-1:0<Sc?1:0}var T;0===(T=y(h.getFullYear()-w.getFullYear()))&&0===(T=y(h.getMonth()-w.getMonth()))&&(T=y(h.getDate()-w.getDate()));return T}function n(h){switch(h.getDay()){case 0:return new Date(h.getFullYear()-1,11,29);case 1:return h;case 2:return new Date(h.getFullYear(),0,3);case 3:return new Date(h.getFullYear(),
0,2);case 4:return new Date(h.getFullYear(),0,1);case 5:return new Date(h.getFullYear()-1,11,31);case 6:return new Date(h.getFullYear()-1,11,30)}}function r(h){var w=h.$a;for(h=new Date((new Date(h.ab+1900,0,1)).getTime());0<w;){var y=h.getMonth(),T=(sd(h.getFullYear())?td:ud)[y];if(w>T-h.getDate())w-=T-h.getDate()+1,h.setDate(1),11>y?h.setMonth(y+1):(h.setMonth(0),h.setFullYear(h.getFullYear()+1));else{h.setDate(h.getDate()+w);break}}y=new Date(h.getFullYear()+1,0,4);w=n(new Date(h.getFullYear(),
0,4));y=n(y);return 0>=l(w,h)?0>=l(y,h)?h.getFullYear()+1:h.getFullYear():h.getFullYear()-1}var p=D[d+40>>2];d={nc:D[d>>2],mc:D[d+4>>2],mb:D[d+8>>2],xb:D[d+12>>2],nb:D[d+16>>2],ab:D[d+20>>2],Va:D[d+24>>2],$a:D[d+28>>2],Jc:D[d+32>>2],lc:D[d+36>>2],oc:p?I(p):""};c=I(c);p={"%c":"%a %b %d %H:%M:%S %Y","%D":"%m/%d/%y","%F":"%Y-%m-%d","%h":"%b","%r":"%I:%M:%S %p","%R":"%H:%M","%T":"%H:%M:%S","%x":"%m/%d/%y","%X":"%H:%M:%S","%Ec":"%c","%EC":"%C","%Ex":"%m/%d/%y","%EX":"%H:%M:%S","%Ey":"%y","%EY":"%Y","%Od":"%d",
"%Oe":"%e","%OH":"%H","%OI":"%I","%Om":"%m","%OM":"%M","%OS":"%S","%Ou":"%u","%OU":"%U","%OV":"%V","%Ow":"%w","%OW":"%W","%Oy":"%y"};for(var u in p)c=c.replace(new RegExp(u,"g"),p[u]);var x="Sunday Monday Tuesday Wednesday Thursday Friday Saturday".split(" "),z="January February March April May June July August September October November December".split(" ");p={"%a":function(h){return x[h.Va].substring(0,3)},"%A":function(h){return x[h.Va]},"%b":function(h){return z[h.nb].substring(0,3)},"%B":function(h){return z[h.nb]},
"%C":function(h){return g((h.ab+1900)/100|0,2)},"%d":function(h){return g(h.xb,2)},"%e":function(h){return f(h.xb,2," ")},"%g":function(h){return r(h).toString().substring(2)},"%G":function(h){return r(h)},"%H":function(h){return g(h.mb,2)},"%I":function(h){h=h.mb;0==h?h=12:12<h&&(h-=12);return g(h,2)},"%j":function(h){for(var w=0,y=0;y<=h.nb-1;w+=(sd(h.ab+1900)?td:ud)[y++]);return g(h.xb+w,3)},"%m":function(h){return g(h.nb+1,2)},"%M":function(h){return g(h.mc,2)},"%n":function(){return"\n"},"%p":function(h){return 0<=
h.mb&&12>h.mb?"AM":"PM"},"%S":function(h){return g(h.nc,2)},"%t":function(){return"\t"},"%u":function(h){return h.Va||7},"%U":function(h){return g(Math.floor((h.$a+7-h.Va)/7),2)},"%V":function(h){var w=Math.floor((h.$a+7-(h.Va+6)%7)/7);2>=(h.Va+371-h.$a-2)%7&&w++;if(w)53==w&&(y=(h.Va+371-h.$a)%7,4==y||3==y&&sd(h.ab)||(w=1));else{w=52;var y=(h.Va+7-h.$a-1)%7;(4==y||5==y&&sd(h.ab%400-1))&&w++}return g(w,2)},"%w":function(h){return h.Va},"%W":function(h){return g(Math.floor((h.$a+7-(h.Va+6)%7)/7),2)},
"%y":function(h){return(h.ab+1900).toString().substring(2)},"%Y":function(h){return h.ab+1900},"%z":function(h){h=h.lc;var w=0<=h;h=Math.abs(h)/60;return(w?"+":"-")+String("0000"+(h/60*100+h%60)).slice(-4)},"%Z":function(h){return h.oc},"%%":function(){return"%"}};c=c.replace(/%%/g,"\x00\x00");for(u in p)c.includes(u)&&(c=c.replace(new RegExp(u,"g"),p[u](d)));c=c.replace(/\0\0/g,"%");u=gc(c,!1);if(u.length>b)return 0;B.set(u,a);return u.length-1}e.requestFullscreen=function(a,b){ob(a,b)};
e.requestAnimationFrame=function(a){tb(a)};e.setCanvasSize=function(a,b,c){Ab(a,b,c)};e.pauseMainLoop=function(){J=null;K++};e.resumeMainLoop=function(){K++;var a=Ua,b=Va,c=Sa;Sa=null;Cb(c,0,!1,Ta,!0);Bb(a,b);J()};e.getUserMedia=function(){window.getUserMedia||(window.getUserMedia=navigator.getUserMedia||navigator.mozGetUserMedia);window.getUserMedia(void 0)};e.createContext=function(a,b,c,d){return gb(a,b,c,d)};var M;
function Cc(a,b,c,d){a||(a=this);this.parent=a;this.Xa=a.Xa;this.kb=null;this.id=vc++;this.name=b;this.mode=c;this.Qa={};this.Ra={};this.rdev=d}Object.defineProperties(Cc.prototype,{read:{get:function(){return 365===(this.mode&365)},set:function(a){a?this.mode|=365:this.mode&=-366}},write:{get:function(){return 146===(this.mode&146)},set:function(a){a?this.mode|=146:this.mode&=-147}}});Oc();wc=Array(4096);Ic(U,"/");W("/tmp",16895,0);W("/home",16895,0);W("/home/<USER>",16895,0);
(()=>{W("/dev",16895,0);jc(259,{read:()=>0,write:(d,f,g,l)=>l});Jc("/dev/null",259);ic(1280,lc);ic(1536,mc);Jc("/dev/tty",1280);Jc("/dev/tty1",1536);var a=new Uint8Array(1024),b=0,c=()=>{0===b&&(b=ac(a).byteLength);return a[--b]};Tc("random",c);Tc("urandom",c);W("/dev/shm",16895,0);W("/dev/shm/tmp",16895,0)})();
(()=>{W("/proc",16895,0);var a=W("/proc/self",16895,0);W("/proc/self/fd",16895,0);Ic({Xa:()=>{var b=oc(a,"fd",16895,73);b.Qa={lookup:(c,d)=>{var f=uc[+d];if(!f)throw new S(8);c={parent:null,Xa:{Kb:"fake"},Qa:{readlink:()=>f.path}};return c.parent=c}};return b}},"/proc/self/fd")})();
var wd={o:function(a,b,c,d){v(`Assertion failed: ${I(a)}, at: `+[b?I(b):"unknown filename",c,d?I(d):"unknown function"])},m:function(a,b,c){(new Ia(a)).Wa(b,c);Ja=a;Ka++;throw Ja;},P:function(){return!0},r:function(){v("")},R:function(){return Date.now()},O:La,S:function(a,b,c){C.copyWithin(a,b,b+c)},M:function(a){var b=C.length;a>>>=0;if(2147483648<a)return!1;for(var c=1;4>=c;c*=2){var d=b*(1+.2/c);d=Math.min(d,a+100663296);var f=Math,g=f.min;d=Math.max(a,d);d+=(65536-d%65536)%65536;a:{var l=ma.buffer;
try{ma.grow(g.call(f,2147483648,d)-l.byteLength+65535>>>16);ra();var n=1;break a}catch(r){}n=void 0}if(n)return!0}return!1},Z:function(a){eval(I(a))},_:function(a,b,c){a=N(a);Cb(a,b,c)},K:function(a,b){var c=0;Fb().forEach(function(d,f){var g=b+c;f=E[a+4*f>>2]=g;for(g=0;g<d.length;++g)B[f++>>0]=d.charCodeAt(g);B[f>>0]=0;c+=d.length+1});return 0},L:function(a,b){var c=Fb();E[a>>2]=c.length;var d=0;c.forEach(function(f){d+=f.length+1});E[b>>2]=d;return 0},b:Na,N:function(){return 52},I:function(){return 70},
u:function(a,b,c,d){for(var f=0,g=0;g<c;g++){var l=E[b>>2],n=E[b+4>>2];b+=8;for(var r=0;r<n;r++){var p=C[l+r],u=Hb[a];0===p||10===p?((1===a?q:t)(H(u,0)),u.length=0):u.push(p)}f+=n}E[d>>2]=f;return 0},t:function(a,b){M.attachShader(O[a],P[b])},a:function(a,b){35051==a?M.pb=b:35052==a&&(M.qb=b);M.bindBuffer(a,Jb[b])},p:function(a,b){M.bindFramebuffer(a,Kb[b])},ma:function(a,b){M.bindTexture(a,Lb[b])},$:function(a){M.bindVertexArray(Mb[a])},c:function(a,b,c,d){c&&b?M.bufferData(a,C,d,c,b):M.bufferData(a,
b,d)},ja:function(a){return M.checkFramebufferStatus(a)},T:function(a){M.clear(a)},U:function(a,b,c,d){M.clearColor(a,b,c,d)},z:function(a){M.compileShader(P[a])},Q:function(){var a=Ob(O),b=M.createProgram();b.name=a;b.ib=b.gb=b.hb=0;b.yb=1;O[a]=b;return a},F:function(a){var b=Ob(P);P[b]=M.createShader(a);return b},k:function(a,b){for(var c=0;c<a;c++){var d=D[b+4*c>>2],f=Jb[d];f&&(M.deleteBuffer(f),f.name=0,Jb[d]=null,d==M.pb&&(M.pb=0),d==M.qb&&(M.qb=0))}},ha:function(a,b){for(var c=0;c<a;++c){var d=
D[b+4*c>>2],f=Kb[d];f&&(M.deleteFramebuffer(f),f.name=0,Kb[d]=null)}},E:function(a){if(a){var b=O[a];b?(M.deleteProgram(b),b.name=0,O[a]=null):Q(1281)}},n:function(a){if(a){var b=P[a];b?(M.deleteShader(b),P[a]=null):Q(1281)}},ia:function(a,b){for(var c=0;c<a;c++){var d=D[b+4*c>>2],f=Lb[d];f&&(M.deleteTexture(f),f.name=0,Lb[d]=null)}},V:function(a){M.depthFunc(a)},q:function(a,b){M.detachShader(O[a],P[b])},A:function(a){M.disable(a)},i:function(a){M.disableVertexAttribArray(a)},ga:function(a,b,c){M.drawArrays(a,
b,c)},v:function(a,b,c,d){M.drawElements(a,b,c,d)},W:function(a){M.enable(a)},f:function(a){M.enableVertexAttribArray(a)},ka:function(a,b,c,d,f){M.framebufferTexture2D(a,b,c,Lb[d],f)},d:function(a,b){Rb(a,b,"createBuffer",Jb)},oa:function(a,b){Rb(a,b,"createFramebuffer",Kb)},na:function(a,b){Rb(a,b,"createTexture",Lb)},aa:function(a,b){Rb(a,b,"createVertexArray",Mb)},h:function(a,b){return M.getAttribLocation(O[a],I(b))},pa:function(a,b,c,d){a=M.getProgramInfoLog(O[a]);null===a&&(a="(unknown error)");
b=0<b&&d?Sb(a,C,d,b):0;c&&(D[c>>2]=b)},G:function(a,b,c){if(c)if(a>=Ib)Q(1281);else if(a=O[a],35716==b)a=M.getProgramInfoLog(a),null===a&&(a="(unknown error)"),D[c>>2]=a.length+1;else if(35719==b){if(!a.ib)for(b=0;b<M.getProgramParameter(a,35718);++b)a.ib=Math.max(a.ib,M.getActiveUniform(a,b).name.length+1);D[c>>2]=a.ib}else if(35722==b){if(!a.gb)for(b=0;b<M.getProgramParameter(a,35721);++b)a.gb=Math.max(a.gb,M.getActiveAttrib(a,b).name.length+1);D[c>>2]=a.gb}else if(35381==b){if(!a.hb)for(b=0;b<
M.getProgramParameter(a,35382);++b)a.hb=Math.max(a.hb,M.getActiveUniformBlockName(a,b).length+1);D[c>>2]=a.hb}else D[c>>2]=M.getProgramParameter(a,b);else Q(1281)},x:function(a,b,c,d){a=M.getShaderInfoLog(P[a]);null===a&&(a="(unknown error)");b=0<b&&d?Sb(a,C,d,b):0;c&&(D[c>>2]=b)},y:function(a,b,c){c?35716==b?(a=M.getShaderInfoLog(P[a]),null===a&&(a="(unknown error)"),D[c>>2]=a?a.length+1:0):35720==b?(a=M.getShaderSource(P[a]),D[c>>2]=a?a.length+1:0):D[c>>2]=M.getShaderParameter(P[a],b):Q(1281)},
e:function(a,b){b=I(b);if(a=O[a]){var c=a,d=c.eb,f=c.Tb,g;if(!d)for(c.eb=d={},c.Sb={},g=0;g<M.getProgramParameter(c,35718);++g){var l=M.getActiveUniform(c,g);var n=l.name;l=l.size;var r=Tb(n);r=0<r?n.slice(0,r):n;var p=c.yb;c.yb+=l;f[r]=[l,p];for(n=0;n<l;++n)d[p]=n,c.Sb[p++]=r}c=a.eb;d=0;f=b;g=Tb(b);0<g&&(d=parseInt(b.slice(g+1))>>>0,f=b.slice(0,g));if((f=a.Tb[f])&&d<f[0]&&(d+=f[1],c[d]=c[d]||M.getUniformLocation(a,b)))return d}else Q(1281);return-1},H:function(a){a=O[a];M.linkProgram(a);a.eb=0;a.Tb=
{}},fa:function(a,b,c,d,f,g,l){if(M.pb)M.readPixels(a,b,c,d,f,g,l);else{var n=Ub(g);M.readPixels(a,b,c,d,f,g,n,l>>31-Math.clz32(n.BYTES_PER_ELEMENT))}},C:function(a,b,c,d){for(var f="",g=0;g<b;++g){var l=d?D[d+4*g>>2]:-1;f+=I(D[c+4*g>>2],0>l?void 0:l)}M.shaderSource(P[a],f)},la:function(a,b,c,d,f,g,l,n,r){if(M.qb)M.texImage2D(a,b,c,d,f,g,l,n,r);else if(r){var p=Ub(n);M.texImage2D(a,b,c,d,f,g,l,n,p,r>>31-Math.clz32(p.BYTES_PER_ELEMENT))}else M.texImage2D(a,b,c,d,f,g,l,n,null)},D:function(a,b,c){M.texParameteri(a,
b,c)},j:function(a,b){M.uniform1f(Vb(a),b)},w:function(a,b,c,d){b&&M.uniformMatrix4fv(Vb(a),!!c,qa,d>>2,16*b)},l:function(a){a=O[a];M.useProgram(a);M.ac=a},g:function(a,b,c,d,f,g){M.vertexAttribPointer(a,b,c,!!d,f,g)},B:function(a,b,c,d){M.viewport(a,b,c,d)},da:function(a,b,c,d){var f;for(f=0;f<Y.length&&null!==Y[f];f++);if(0<f)throw"glfwCreateWindow only supports one window at time currently";var g=f+1;if(0>=a||0>=b)g=0;else{d?ob():Ab(a,b);for(f=0;f<Y.length&&null==Y[f];f++);d=0<R[139265];f==Y.length&&
(d?e.cb=gb(e.canvas,!0,!0,{antialias:1<R[135181],depth:0<R[135173],stencil:0<R[135174],alpha:0<R[135172]}):db());!e.cb&&d?g=0:(a=new Wb(g,a,b,c),g-1==Y.length?Y.push(a):Y[g-1]=a,X=a,g=a.id)}return g},ea:function(){if(Y)return 1;La();R=Vc;Y=[];X=null;Uc="number"==typeof devicePixelRatio&&devicePixelRatio||1;window.addEventListener("gamepadconnected",$c,!0);window.addEventListener("gamepaddisconnected",bd,!0);window.addEventListener("keydown",cd,!0);window.addEventListener("keypress",Yc,!0);window.addEventListener("keyup",
dd,!0);window.addEventListener("blur",ed,!0);(function b(){window.matchMedia("(resolution: "+window.devicePixelRatio+"dppx)").addEventListener("change",b,{once:!0});Uc="number"==typeof devicePixelRatio&&devicePixelRatio||1;X&&X.Ub&&N(X.Ub)(X.id,Uc,Uc)})();e.canvas.addEventListener("touchmove",fd,!0);e.canvas.addEventListener("touchstart",kd,!0);e.canvas.addEventListener("touchcancel",ld,!0);e.canvas.addEventListener("touchend",ld,!0);e.canvas.addEventListener("mousemove",fd,!0);e.canvas.addEventListener("mousedown",
kd,!0);e.canvas.addEventListener("mouseup",ld,!0);e.canvas.addEventListener("wheel",md,!0);e.canvas.addEventListener("mousewheel",md,!0);e.canvas.addEventListener("mouseenter",gd,!0);e.canvas.addEventListener("mouseleave",hd,!0);e.canvas.addEventListener("drop",qd,!0);e.canvas.addEventListener("dragover",rd,!0);zb.push((b,c)=>{if(X){var d=!0;document.fullscreen||document.fullScreen||document.mozFullScreen||document.webkitIsFullScreen?(X.Qb=X.x,X.Rb=X.y,X.Pb=X.width,X.Ob=X.height,X.x=X.y=0,X.width=
screen.width,X.height=screen.height,X.ub=!0):1==X.ub?(X.x=X.Qb,X.y=X.Rb,X.width=X.Pb,X.height=X.Ob,X.ub=!1):X.width!=b||X.height!=c?(X.width=b,X.height=c):d=!1;d&&(Ab(X.width,X.height,!0),X&&X.Vb&&N(X.Vb)(X.id,X.width,X.height),X&&X.Gb&&N(X.Gb)(X.id,X.width,X.height))}});return 1},ba:function(){},X:function(){},Y:function(){},ca:function(){window.removeEventListener("gamepadconnected",$c,!0);window.removeEventListener("gamepaddisconnected",bd,!0);window.removeEventListener("keydown",cd,!0);window.removeEventListener("keypress",
Yc,!0);window.removeEventListener("keyup",dd,!0);window.removeEventListener("blur",ed,!0);e.canvas.removeEventListener("touchmove",fd,!0);e.canvas.removeEventListener("touchstart",kd,!0);e.canvas.removeEventListener("touchcancel",ld,!0);e.canvas.removeEventListener("touchend",ld,!0);e.canvas.removeEventListener("mousemove",fd,!0);e.canvas.removeEventListener("mousedown",kd,!0);e.canvas.removeEventListener("mouseup",ld,!0);e.canvas.removeEventListener("wheel",md,!0);e.canvas.removeEventListener("mousewheel",
md,!0);e.canvas.removeEventListener("mouseenter",gd,!0);e.canvas.removeEventListener("mouseleave",hd,!0);e.canvas.removeEventListener("drop",qd,!0);e.canvas.removeEventListener("dragover",rd,!0);e.canvas.width=e.canvas.height=1;X=Y=null},s:function(a,b){R[a]=b},J:function(a,b,c,d){return vd(a,b,c,d)}};
(function(){function a(c){c=c.exports;e.asm=c;ma=e.asm.qa;ra();sa=e.asm.La;ua.unshift(e.asm.ra);F--;e.monitorRunDependencies&&e.monitorRunDependencies(F);if(0==F&&(null!==ya&&(clearInterval(ya),ya=null),za)){var d=za;za=null;d()}return c}var b={a:wd};F++;e.monitorRunDependencies&&e.monitorRunDependencies(F);if(e.instantiateWasm)try{return e.instantiateWasm(b,a)}catch(c){return t("Module.instantiateWasm callback failed with error: "+c),!1}Fa(b,function(c){a(c.instance)});return{}})();
function Zb(){return(Zb=e.asm.sa).apply(null,arguments)}function pd(){return(pd=e.asm.ta).apply(null,arguments)}e._updateShaderFunction=function(){return(e._updateShaderFunction=e.asm.ua).apply(null,arguments)};e._setRenderNeeded=function(){return(e._setRenderNeeded=e.asm.va).apply(null,arguments)};e._translateState=function(){return(e._translateState=e.asm.wa).apply(null,arguments)};e._scaleState=function(){return(e._scaleState=e.asm.xa).apply(null,arguments)};
e._resizeWindow=function(){return(e._resizeWindow=e.asm.ya).apply(null,arguments)};e._resetState=function(){return(e._resetState=e.asm.za).apply(null,arguments)};e._setScreenCenter=function(){return(e._setScreenCenter=e.asm.Aa).apply(null,arguments)};e._setMeshShowEdges=function(){return(e._setMeshShowEdges=e.asm.Ba).apply(null,arguments)};e._setMeshSmoothShading=function(){return(e._setMeshSmoothShading=e.asm.Ca).apply(null,arguments)};
e._setMeshBothLeafs=function(){return(e._setMeshBothLeafs=e.asm.Da).apply(null,arguments)};e._isModelEmpty=function(){return(e._isModelEmpty=e.asm.Ea).apply(null,arguments)};e._getFileSize=function(){return(e._getFileSize=e.asm.Fa).apply(null,arguments)};e._generateSTL=function(){return(e._generateSTL=e.asm.Ga).apply(null,arguments)};e._generatePLY=function(){return(e._generatePLY=e.asm.Ha).apply(null,arguments)};e._generateOBJ=function(){return(e._generateOBJ=e.asm.Ia).apply(null,arguments)};
e._generateGLB=function(){return(e._generateGLB=e.asm.Ja).apply(null,arguments)};var xd=e._main=function(){return(xd=e._main=e.asm.Ka).apply(null,arguments)};function yd(){return(yd=e.asm.Ma).apply(null,arguments)}function zd(){return(zd=e.asm.Na).apply(null,arguments)}function Ad(){return(Ad=e.asm.Oa).apply(null,arguments)}
e.ccall=function(a,b,c,d){var f={string:p=>{var u=0;if(null!==p&&void 0!==p&&0!==p){u=Xb(p)+1;var x=Ad(u);Sb(p,C,x,u);u=x}return u},array:p=>{var u=Ad(p.length);B.set(p,u);return u}};a=e["_"+a];var g=[],l=0;if(d)for(var n=0;n<d.length;n++){var r=f[c[n]];r?(0===l&&(l=yd()),g[n]=r(d[n])):g[n]=d[n]}c=a.apply(null,g);return c=function(p){0!==l&&zd(l);return"string"===b?I(p):"boolean"===b?!!p:p}(c)};var Bd;za=function Cd(){Bd||Dd();Bd||(za=Cd)};
function Dd(){function a(){if(!Bd&&(Bd=!0,e.calledRun=!0,!A)){e.noFSInit||Pc||(Pc=!0,Oc(),e.stdin=e.stdin,e.stdout=e.stdout,e.stderr=e.stderr,e.stdin?Tc("stdin",e.stdin):Kc("/dev/tty","/dev/stdin"),e.stdout?Tc("stdout",null,e.stdout):Kc("/dev/tty","/dev/stdout"),e.stderr?Tc("stderr",null,e.stderr):Kc("/dev/tty1","/dev/stderr"),Mc("/dev/stdin",0),Mc("/dev/stdout",1),Mc("/dev/stderr",1));xc=!1;Ga(ua);Ga(va);if(e.onRuntimeInitialized)e.onRuntimeInitialized();if(Ed){var b=xd;try{var c=b(0,0);Na(c,!0)}catch(d){Ma(d)}}if(e.postRun)for("function"==
typeof e.postRun&&(e.postRun=[e.postRun]);e.postRun.length;)b=e.postRun.shift(),wa.unshift(b);Ga(wa)}}if(!(0<F)){if(e.preRun)for("function"==typeof e.preRun&&(e.preRun=[e.preRun]);e.preRun.length;)xa();Ga(ta);0<F||(e.setStatus?(e.setStatus("Running..."),setTimeout(function(){setTimeout(function(){e.setStatus("")},1);a()},1)):a())}}if(e.preInit)for("function"==typeof e.preInit&&(e.preInit=[e.preInit]);0<e.preInit.length;)e.preInit.pop()();var Ed=!0;e.noInitialRun&&(Ed=!1);Dd();
