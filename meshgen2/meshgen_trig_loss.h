/* This file was automatically generated by CasADi 3.6.3.
 *  It consists of: 
 *   1) content generated by CasADi runtime: not copyrighted
 *   2) template code copied from CasADi source: permissively licensed (MIT-0)
 *   3) user code: owned by the user
 *
 */
#ifdef __cplusplus
extern "C" {
#endif

/* How to prefix internal symbols */
#ifdef CASADI_CODEGEN_PREFIX
  #define CASADI_NAMESPACE_CONCAT(NS, ID) _CASADI_NAMESPACE_CONCAT(NS, ID)
  #define _CASADI_NAMESPACE_CONCAT(NS, ID) NS ## ID
  #define CASADI_PREFIX(ID) CASADI_NAMESPACE_CONCAT(CODEGEN_PREFIX, ID)
#else
  #define CASADI_PREFIX(ID) meshgen_trig_loss_ ## ID
#endif

#include <math.h>

#ifndef casadi_real
#define casadi_real float
#endif

#ifndef casadi_int
#define casadi_int long long int
#endif

/* Add prefix to internal symbols */
#define casadi_f0 CASADI_PREFIX(f0)
#define casadi_fmax CASADI_PREFIX(fmax)
#define casadi_s0 CASADI_PREFIX(s0)
#define casadi_s1 CASADI_PREFIX(s1)
#define casadi_s2 CASADI_PREFIX(s2)
#define casadi_sq CASADI_PREFIX(sq)

/* Symbol visibility in DLLs */
#ifndef CASADI_SYMBOL_EXPORT
  #if defined(_WIN32) || defined(__WIN32__) || defined(__CYGWIN__)
    #if defined(STATIC_LINKED)
      #define CASADI_SYMBOL_EXPORT
    #else
      #define CASADI_SYMBOL_EXPORT __declspec(dllexport)
    #endif
  #elif defined(__GNUC__) && defined(GCC_HASCLASSVISIBILITY)
    #define CASADI_SYMBOL_EXPORT __attribute__ ((visibility ("default")))
  #else
    #define CASADI_SYMBOL_EXPORT
  #endif
#endif

casadi_real casadi_sq(casadi_real x) { return x*x;}

casadi_real casadi_fmax(casadi_real x, casadi_real y) {
/* Pre-c99 compatibility */
#if __STDC_VERSION__ < 199901L
  return x>y ? x : y;
#else
  return fmax(x, y);
#endif
}

static const casadi_int casadi_s0[10] = {6, 1, 0, 6, 0, 1, 2, 3, 4, 5};
static const casadi_int casadi_s1[5] = {1, 1, 0, 1, 0};
static const casadi_int casadi_s2[15] = {1, 6, 0, 1, 2, 3, 4, 5, 6, 0, 0, 0, 0, 0, 0};

/* meshgen_trig_loss:(i0[6])->(o0,o1[1x6],o2) */
static int casadi_f0(const casadi_real** arg, casadi_real** res, casadi_int* iw, casadi_real* w, int mem) {
  casadi_real a0, a1, a10, a11, a12, a13, a14, a15, a16, a17, a2, a3, a4, a5, a6, a7, a8, a9;
  a0=arg[0]? arg[0][2] : 0;
  a1=arg[0]? arg[0][0] : 0;
  a2=(a0-a1);
  a3=(a1+a0);
  a4=arg[0]? arg[0][4] : 0;
  a3=(a3+a4);
  a5=3.;
  a3=(a3/a5);
  a6=(a1-a3);
  a7=casadi_sq(a6);
  a8=arg[0]? arg[0][1] : 0;
  a9=arg[0]? arg[0][3] : 0;
  a10=(a8+a9);
  a11=arg[0]? arg[0][5] : 0;
  a10=(a10+a11);
  a10=(a10/a5);
  a5=(a8-a10);
  a12=casadi_sq(a5);
  a7=(a7+a12);
  a0=(a0-a3);
  a12=casadi_sq(a0);
  a7=(a7+a12);
  a12=(a9-a10);
  a13=casadi_sq(a12);
  a7=(a7+a13);
  a3=(a4-a3);
  a13=casadi_sq(a3);
  a7=(a7+a13);
  a10=(a11-a10);
  a13=casadi_sq(a10);
  a7=(a7+a13);
  a13=9.9999999999999998e-13;
  a14=casadi_fmax(a7,a13);
  a14=sqrt(a14);
  a2=(a2/a14);
  a11=(a11-a8);
  a11=(a11/a14);
  a15=(a2*a11);
  a4=(a4-a1);
  a4=(a4/a14);
  a9=(a9-a8);
  a9=(a9/a14);
  a8=(a4*a9);
  a15=(a15-a8);
  a15=(a15+a13);
  a8=log(a15);
  a8=(-a8);
  if (res[0]!=0) res[0][0]=a8;
  a6=(a6+a6);
  a8=(a13<=a7);
  a13=(a7<=a13);
  a13=(a8+a13);
  a8=(a8/a13);
  a13=(a11/a14);
  a1=(a2/a15);
  a13=(a13*a1);
  a16=(a9/a14);
  a17=(a4/a15);
  a16=(a16*a17);
  a4=(a4/a14);
  a9=(a9/a15);
  a4=(a4*a9);
  a16=(a16+a4);
  a13=(a13-a16);
  a2=(a2/a14);
  a11=(a11/a15);
  a2=(a2*a11);
  a13=(a13+a2);
  a2=(a14+a14);
  a13=(a13/a2);
  a8=(a8*a13);
  a6=(a6*a8);
  a9=(a9/a14);
  a13=(a6-a9);
  a2=3.3333333333333331e-01;
  a3=(a3+a3);
  a3=(a3*a8);
  a0=(a0+a0);
  a0=(a0*a8);
  a15=(a3+a0);
  a15=(a15+a6);
  a15=(a2*a15);
  a13=(a13-a15);
  a11=(a11/a14);
  a13=(a13+a11);
  if (res[1]!=0) res[1][0]=a13;
  a1=(a1/a14);
  a17=(a17/a14);
  a14=(a1-a17);
  a5=(a5+a5);
  a5=(a5*a8);
  a14=(a14+a5);
  a10=(a10+a10);
  a10=(a10*a8);
  a12=(a12+a12);
  a12=(a12*a8);
  a8=(a10+a12);
  a8=(a8+a5);
  a2=(a2*a8);
  a14=(a14-a2);
  if (res[1]!=0) res[1][1]=a14;
  a0=(a0-a15);
  a0=(a0-a11);
  if (res[1]!=0) res[1][2]=a0;
  a17=(a17+a12);
  a17=(a17-a2);
  if (res[1]!=0) res[1][3]=a17;
  a9=(a9+a3);
  a9=(a9-a15);
  if (res[1]!=0) res[1][4]=a9;
  a10=(a10-a1);
  a10=(a10-a2);
  if (res[1]!=0) res[1][5]=a10;
  if (res[2]!=0) res[2][0]=a7;
  return 0;
}

CASADI_SYMBOL_EXPORT int meshgen_trig_loss(const casadi_real** arg, casadi_real** res, casadi_int* iw, casadi_real* w, int mem){
  return casadi_f0(arg, res, iw, w, mem);
}

CASADI_SYMBOL_EXPORT int meshgen_trig_loss_alloc_mem(void) {
  return 0;
}

CASADI_SYMBOL_EXPORT int meshgen_trig_loss_init_mem(int mem) {
  return 0;
}

CASADI_SYMBOL_EXPORT void meshgen_trig_loss_free_mem(int mem) {
}

CASADI_SYMBOL_EXPORT int meshgen_trig_loss_checkout(void) {
  return 0;
}

CASADI_SYMBOL_EXPORT void meshgen_trig_loss_release(int mem) {
}

CASADI_SYMBOL_EXPORT void meshgen_trig_loss_incref(void) {
}

CASADI_SYMBOL_EXPORT void meshgen_trig_loss_decref(void) {
}

CASADI_SYMBOL_EXPORT casadi_int meshgen_trig_loss_n_in(void) { return 1;}

CASADI_SYMBOL_EXPORT casadi_int meshgen_trig_loss_n_out(void) { return 3;}

CASADI_SYMBOL_EXPORT casadi_real meshgen_trig_loss_default_in(casadi_int i) {
  switch (i) {
    default: return 0;
  }
}

CASADI_SYMBOL_EXPORT const char* meshgen_trig_loss_name_in(casadi_int i) {
  switch (i) {
    case 0: return "i0";
    default: return 0;
  }
}

CASADI_SYMBOL_EXPORT const char* meshgen_trig_loss_name_out(casadi_int i) {
  switch (i) {
    case 0: return "o0";
    case 1: return "o1";
    case 2: return "o2";
    default: return 0;
  }
}

CASADI_SYMBOL_EXPORT const casadi_int* meshgen_trig_loss_sparsity_in(casadi_int i) {
  switch (i) {
    case 0: return casadi_s0;
    default: return 0;
  }
}

CASADI_SYMBOL_EXPORT const casadi_int* meshgen_trig_loss_sparsity_out(casadi_int i) {
  switch (i) {
    case 0: return casadi_s1;
    case 1: return casadi_s2;
    case 2: return casadi_s1;
    default: return 0;
  }
}

CASADI_SYMBOL_EXPORT int meshgen_trig_loss_work(casadi_int *sz_arg, casadi_int* sz_res, casadi_int *sz_iw, casadi_int *sz_w) {
  if (sz_arg) *sz_arg = 1;
  if (sz_res) *sz_res = 3;
  if (sz_iw) *sz_iw = 0;
  if (sz_w) *sz_w = 0;
  return 0;
}


#ifdef __cplusplus
} /* extern "C" */
#endif
