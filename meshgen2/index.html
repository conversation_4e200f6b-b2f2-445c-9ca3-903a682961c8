<!doctype html>
<html lang="en-us">
<head>
    <meta charset="utf-8">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Meshgen2 | Spirulae</title>

    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <link rel="canonical" href="https://harry7557558.github.io/spirulae/meshgen2/" />

    <style>
      body {
        font-family: arial;
        margin: 0;
        padding: none;
      }

      .emscripten { padding-right: 0; margin-left: auto; margin-right: auto; display: block; }
      canvas.emscripten { border: 0px none; background-color: black; }

      #emscripten_logo {
        display: inline-block;
        margin: 0;
      }

      .spinner {
        height: 30px;
        width: 30px;
        margin: 0;
        margin-top: 20px;
        margin-left: 20px;
        display: inline-block;
        vertical-align: top;

        -webkit-animation: rotation .8s linear infinite;
        -moz-animation: rotation .8s linear infinite;
        -o-animation: rotation .8s linear infinite;
        animation: rotation 0.8s linear infinite;

        border-left: 5px solid rgb(235, 235, 235);
        border-right: 5px solid rgb(235, 235, 235);
        border-bottom: 5px solid rgb(235, 235, 235);
        border-top: 5px solid rgb(120, 120, 120);
        
        border-radius: 100%;
        background-color: rgb(189, 215, 46);
      }

      @-webkit-keyframes rotation {
        from {-webkit-transform: rotate(0deg);}
        to {-webkit-transform: rotate(360deg);}
      }
      @-moz-keyframes rotation {
        from {-moz-transform: rotate(0deg);}
        to {-moz-transform: rotate(360deg);}
      }
      @-o-keyframes rotation {
        from {-o-transform: rotate(0deg);}
        to {-o-transform: rotate(360deg);}
      }
      @keyframes rotation {
        from {transform: rotate(0deg);}
        to {transform: rotate(360deg);}
      }

      #status {
        display: inline-block;
        vertical-align: top;
        margin-top: 30px;
        margin-left: 20px;
        font-weight: bold;
        color: rgb(120, 120, 120);
      }

      #progress {
        height: 20px;
        width: 300px;
      }

      #controls {
        display: inline-block;
        vertical-align: top;
        margin-right: 20px;
      }

      #output {
        width: 100%;
        height: 300px;
        margin: 0 auto;
        margin-top: 10px;
        border-left: 0px;
        border-right: 0px;
        padding-left: 0px;
        padding-right: 0px;
        display: block;
        background-color: black;
        color: white;
        font-family: 'Lucida Console', Monaco, monospace;
        outline: none;
      }
    </style>

    <link rel="stylesheet" href="../styles/style.css" />

</head>
<body>

    <div class="spinner" id='spinner'></div>
    <div class="emscripten" id="status">Downloading...</div>

<span id='controls'>
  <span><input type="checkbox" id="resize">Resize canvas</span>
  <span><input type="checkbox" id="pointerLock" checked>Lock/hide mouse pointer &nbsp;&nbsp;&nbsp;</span>
  <span><input type="button" value="Fullscreen" onclick="Module.requestFullscreen(document.getElementById('pointerLock').checked, 
                                                                            document.getElementById('resize').checked)">
  </span>
</span>

    <div class="emscripten">
      <progress value="0" max="100" id="progress" hidden=1></progress>
    </div>

    
    <div class="emscripten_border">
      <canvas class="emscripten" id="emscripten-canvas" oncontextmenu="event.preventDefault()" tabindex=-1></canvas>
    </div>
    <textarea id="output" rows="8" style="display:none"></textarea>

    <script type='text/javascript'>
      var statusElement = document.getElementById('status');
      var progressElement = document.getElementById('progress');
      var spinnerElement = document.getElementById('spinner');

      var Module = {
        preRun: [],
        postRun: [],
        print: (function() {
          var element = document.getElementById('output');
          if (element) element.value = ''; // clear browser cache
          return function(text) {
            if (arguments.length > 1) text = Array.prototype.slice.call(arguments).join(' ');
            // These replacements are necessary if you render to raw HTML
            //text = text.replace(/&/g, "&amp;");
            //text = text.replace(/</g, "&lt;");
            //text = text.replace(/>/g, "&gt;");
            //text = text.replace('\n', '<br>', 'g');
            console.log(text);
            if (element) {
              element.value += text + "\n";
              element.scrollTop = element.scrollHeight; // focus on bottom
            }
          };
        })(),
        canvas: (() => {
          var canvas = document.getElementById('emscripten-canvas');

          // As a default initial behavior, pop up an alert when webgl context is lost. To make your
          // application robust, you may want to override this behavior before shipping!
          // See http://www.khronos.org/registry/webgl/specs/latest/1.0/#5.15.2
          canvas.addEventListener("webglcontextlost", (e) => { alert('WebGL context lost. You will need to reload the page.'); e.preventDefault(); }, false);

          return canvas;
        })(),
        setStatus: (text) => {
          if (!Module.setStatus.last) Module.setStatus.last = { time: Date.now(), text: '' };
          if (text === Module.setStatus.last.text) return;
          var m = text.match(/([^(]+)\((\d+(\.\d+)?)\/(\d+)\)/);
          var now = Date.now();
          if (m && now - Module.setStatus.last.time < 30) return; // if this is a progress update, skip it if too soon
          Module.setStatus.last.time = now;
          Module.setStatus.last.text = text;
          if (m) {
            text = m[1];
            progressElement.value = parseInt(m[2])*100;
            progressElement.max = parseInt(m[4])*100;
            progressElement.hidden = false;
            spinnerElement.hidden = false;
          } else {
            progressElement.value = null;
            progressElement.max = null;
            progressElement.hidden = true;
            if (!text) spinnerElement.style.display = 'none';
          }
          statusElement.innerHTML = text;
        },
        totalDependencies: 0,
        monitorRunDependencies: (left) => {
          this.totalDependencies = Math.max(this.totalDependencies, left);
          Module.setStatus(left ? 'Preparing... (' + (this.totalDependencies-left) + '/' + this.totalDependencies + ')' : 'All downloads complete.');
        }
      };
      Module.setStatus('Downloading...');
      window.onerror = (event) => {
        // TODO: do not warn on ok events like simulating an infinite loop or exitStatus
        Module.setStatus('Exception thrown, see JavaScript console');
        spinnerElement.style.display = 'none';
        Module.setStatus = (text) => {
          if (text) console.error('[post-exception status] ' + text);
        };
      };
    </script>


    <div id="legend">
        <div id="fps"></div>
        <svg id="axes" width="120" height="40">
            <g transform="translate(0,5)">
                <rect id="legend-scale" x="0" y="0" width="0" height="20" stroke="none" fill="rgba(96,96,96,0.7)" />
                <text id="legend-text" x="0" y="15" text-anchor="middle" alignment-baseline="top" stroke="none"
                    fill="white" font-family="monospace" font-size="1.2em"></text>
            </g>
        </svg>
    </div>

    <div id="control">
        <select id="builtin-functions"></select>
        <span title="Update equation (Alt+Enter)"><button id="button-update">update</button></span>
        <a href='#' style="float:right"
            onclick='javascript:event.preventDefault();document.getElementById("help-menu").style.visibility="visible";'>help</a>
        <br />
        <span title="Display a preview of the input equation"><input type="checkbox" id="checkbox-latex"
                checked />equation preview</span>&ensp;
        <span title="Automatically update the shader on input"><input type="checkbox" id="checkbox-auto-compile"
                checked />auto-update</span>&ensp;
        <br />
        <textarea id="equation-input" spellcheck="false" autocapitalize="off" autocorrect="off"
            data-gramm="false"></textarea>
        <br />
        <span title="show mesh"><input type="checkbox" id="checkbox-edge" checked />mesh</span>&ensp;
        <span title="smooth shading"><input type="checkbox" id="checkbox-normal" checked />normal</span>&ensp;
        <span title="one vs two leaves"><input type="checkbox" id="checkbox-closed" checked />closed</span>
        <span title="Light theme" style="display:none;"><input type="checkbox" id="checkbox-light" checked disabled />☼</span>
        <br />
        <span>Export:
          <input id="export-stl" type="button" value="STL" />
          <input id="export-ply" type="button" value="PLY" />
          <input id="export-obj" type="button" value="OBJ" />
          <input id="export-glb" type="button" value="GLB" />
        </span>
        <br />
        <p id="error-message" style="display:none"></p>
    </div>

    <div id="mathjax-preview" style="left:0px;top:0px;display:none"></div>

    <div id="help-menu" style="visibility:hidden">
        <div id="help-menu-hide" onclick='document.getElementById("help-menu").style.visibility="hidden"'>×</div>
    </div>

    <script>
        (function () {
            // refresh cache every one hour
            function loadScript(src) {
                var hour = Math.floor(Date.now() / 3600000);
                var script = document.createElement("script");
                script.src = src + "?nocache=" + hour;
                document.head.appendChild(script);
            }
            loadScript("module.js");
            loadScript("../scripts/parameter.js");
            loadScript("../scripts/functions.js");
            loadScript("../scripts/parser.js");
            loadScript("../scripts/codegen.js");
            loadScript("../scripts/render-gl.js");
            loadScript("../scripts/render-emscripten.js");
            loadScript("../scripts/latex.js");
            loadScript("script.js");
        })();
    </script>

</body>

</html>
